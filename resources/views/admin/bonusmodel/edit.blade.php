@extends('admin.Public.aside')
@section('title')編輯回饋模組 - J參數設定@endsection

@section('css')
<style>
    .bonus-container {
        max-width: 1000px;
        margin: 0 auto;
        background: #fff;
        padding: 24px 18px;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0,0,0,0.04);
    }
</style>
@endsection

@section('content')
<div>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url('admin/bonusmodel/index') }}">回饋模組</a></li>
            <li class="breadcrumb-item active">編輯模組</li>
        </ol>
    </nav>

    <div class="bonus-container">
        <form method="POST" action="{{ url('admin/bonusmodel/update/' . $data['model']->id) }}">
            @csrf
            @method('PUT')

            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if(isset($errors) && $errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="row">
                <div class="col-md-6 col-12 mb-2">
                    <div class="form-group">
                        <label for="name">模組名稱 <span class="text-danger">*</span></label>
                        <input type="text"
                               class="form-control"
                               id="name"
                               name="name"
                               value="{{ old('name', $data['model']->name) }}"
                               placeholder="請輸入模組名稱"
                               required>
                    </div>
                </div>
                <div class="col-12 mb-2"><hr></div>
            </div>

            <div class="row">
                <div class="col-12 mb-2">
                    <h5>一般回饋設定</h5>
                    <span class="text-danger">(「一般分潤回饋」用，合計應為100%)</span>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">推廣獎勵</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_recommend" value="{{ old('normal_recommend', $data['model']->normal_recommend) }}" placeholder="ex:25" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">合夥平級獎勵</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_partner" value="{{ old('normal_partner', $data['model']->normal_partner) }}" placeholder="ex:25" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">行政/廣告部門</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_marketing_dept" value="{{ old('normal_marketing_dept', $data['model']->normal_marketing_dept) }}" placeholder="ex:5" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">業務部門</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_sales_dept" value="{{ old('normal_sales_dept', $data['model']->normal_sales_dept) }}" placeholder="ex:7" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">大總監</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_executive_director" value="{{ old('normal_executive_director', $data['model']->normal_executive_director) }}" placeholder="ex:1" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">中心總監</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_center_director" value="{{ old('normal_center_director', $data['model']->normal_center_director) }}" placeholder="ex:2" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">中心發起人</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_center_founder" value="{{ old('normal_center_founder', $data['model']->normal_center_founder) }}" placeholder="ex:2" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">講師獎勵</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_lecturer" value="{{ old('normal_lecturer', $data['model']->normal_lecturer) }}" placeholder="ex:3" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">中心獎勵</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_center" value="{{ old('normal_center', $data['model']->normal_center) }}" placeholder="ex:15" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">月分紅</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_dividend_month" value="{{ old('normal_dividend_month', $data['model']->normal_dividend_month) }}" placeholder="ex:20" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">中心獎勵-發起者佔比</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="normal_center_divided_to_raiser" value="{{ old('normal_center_divided_to_raiser', $data['model']->normal_center_divided_to_raiser) }}" placeholder="ex:30" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                    <small class="text-danger">
                        「中心獎勵」將按此比率拆分給其「發起者」。另依「中心等級設定」有「級差」變動。
                    </small>
                </div>
            </div>

            <div class="row">
                <div class="col-12 mb-2"><hr></div>
                <div class="col-12 mb-2">
                    <h5>合夥批發回饋設定</h5>
                    <span class="text-danger">(合計應為100%)</span><br>
                    <span class="text-danger">若消費者自身「會員級別」是任督以下，且其推廣者具「有效合夥人身分」，且模組「有用合夥批發回饋」，計算商品回饋時改採「合夥批發回饋」設定，否則一律採「一般回饋」設定。</span>
                </div>
                <div class="col-md-6 col-12">
                    <div class="form-group">
                        <label for="use_partner_mode">是否使用合夥批發回饋</label>
                        <select name="use_partner_mode" id="use_partner_mode" class="form-control">
                            <option value="0" {{ old('use_partner_mode', $data['model']->use_partner_mode) == '0' ? 'selected' : '' }}>否</option>
                            <option value="1" {{ old('use_partner_mode', $data['model']->use_partner_mode) == '1' ? 'selected' : '' }}>是</option>
                        </select>
                    </div>
                </div>
                <div class="col-12 mb-2"></div>

                <div id="partner_fields" style="display: {{ old('use_partner_mode', $data['model']->use_partner_mode) == '1' ? 'contents' : 'none' }};">
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">推廣獎勵</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_recommend" value="{{ old('partner_recommend', $data['model']->partner_recommend) }}" placeholder="ex:85" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">合夥平級獎勵</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_partner" value="{{ old('partner_partner', $data['model']->partner_partner) }}" placeholder="ex:0" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">行政/廣告部門</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_marketing_dept" value="{{ old('partner_marketing_dept', $data['model']->partner_marketing_dept) }}" placeholder="ex:5" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">業務部門</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_sales_dept" value="{{ old('partner_sales_dept', $data['model']->partner_sales_dept) }}" placeholder="ex:7" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">大總監</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_executive_director" value="{{ old('partner_executive_director', $data['model']->partner_executive_director) }}" placeholder="ex:1" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">中心總監</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_center_director" value="{{ old('partner_center_director', $data['model']->partner_center_director) }}" placeholder="ex:2" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">中心發起人</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_center_founder" value="{{ old('partner_center_founder', $data['model']->partner_center_founder) }}" placeholder="ex:2" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">講師獎勵</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_lecturer" value="{{ old('partner_lecturer', $data['model']->partner_lecturer) }}" placeholder="ex:3" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">中心獎勵</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_center" value="{{ old('partner_center', $data['model']->partner_center) }}" placeholder="ex:0" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">月分紅</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_dividend_month" value="{{ old('partner_dividend_month', $data['model']->partner_dividend_month) }}" placeholder="ex:0" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text">中心獎勵-發起者佔比</div>
                            </div>
                            <input type="number" step="0.01" class="form-control text-right"
                                name="partner_center_divided_to_raiser" value="{{ old('partner_center_divided_to_raiser', $data['model']->partner_center_divided_to_raiser) }}" placeholder="ex:0" min="0" max="100">
                            <div class="input-group-append">
                                <div class="input-group-text">%</div>
                            </div>
                        </div>
                        <small class="text-danger">
                            「中心獎勵」將按此比率拆分給其「發起者」。另依「中心等級設定」有「級差」變動。
                        </small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 mb-2"><hr></div>
                <div class="col-12 mb-2">
                    <h5>廣告推廣獎勵設定</h5>
                    <span class="text-danger">(「總部消費回饋」用，剩餘%數將回饋給系統帳號)</span>
                </div>
                <div class="col-lg-6 col-12 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text">廣告推廣獎勵</div>
                        </div>
                        <input type="number" step="0.01" class="form-control text-right"
                            name="ad_bonus" value="{{ old('ad_bonus', $data['model']->ad_bonus) }}" placeholder="ex:25" min="0" max="100">
                        <div class="input-group-append">
                            <div class="input-group-text">%</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> 更新
                </button>
                <a href="{{ url('admin/bonusmodel/index') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </a>
            </div>
        </form>
    </div>
</div>
@endsection

@section('ownJS')
<script>
$(document).ready(function() {
    // 控制合夥批發回饋設定的顯示/隱藏
    $('#use_partner_mode').on('change', function() {
        if ($(this).val() == '1') {
            $('#partner_fields').show();
        } else {
            $('#partner_fields').hide();
        }
    });
});
</script>
@endsection
