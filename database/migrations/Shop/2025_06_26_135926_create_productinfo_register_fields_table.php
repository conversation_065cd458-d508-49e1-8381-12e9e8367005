<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('productinfo_register_fields', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('fields_set_id')->default(0)->comment('常用欄位id');
            $table->integer('prod_id')->comment('商品id');
            $table->text('title')->comment('標題');
            $table->text('type')->comment('輸入方式');
            $table->tinyInteger('required')->default(0)->comment('必填 0.否 1.是');
            $table->tinyInteger('special')->default(0)->comment('特殊欄位 0.否 1.是');
            $table->text('limit')->nullable()->comment('限定格式');
            $table->text('discription')->default('')->comment('說明');
            $table->text('options')->nullable()->comment('選項(json格式)');
            $table->integer('order_id')->comment('排序');
            $table->tinyInteger('online')->default(1)->comment('狀態 0.停用 1.啟用');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('productinfo_register_fields');
    }
};
