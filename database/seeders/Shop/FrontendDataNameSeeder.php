<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FrontendDataNameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=frontend_data_name --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'name' => '人氣商品',
                'url' => NULL,
                'show_type' => 'tag',
            ],
            [
                'id' => 2,
                'name' => '店長推薦',
                'url' => NULL,
                'show_type' => 'tag',
            ],
            [
                'id' => 3,
                'name' => '即期良品',
                'url' => NULL,
                'show_type' => 'tag',
            ],
            [
                'id' => 4,
                'name' => '特價商品',
                'url' => NULL,
                'show_type' => 'tag',
            ]
        ];
        DB::table('frontend_data_name')->truncate();
        DB::table("frontend_data_name")->insert($dataTables);
    }
}
