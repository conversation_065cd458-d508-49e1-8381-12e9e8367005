<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('points_record', function (Blueprint $table) {
            $table->comment('紅利點數紀錄 天脈:現金積分紀錄');
            $table->integer('id', true);
            $table->integer('user_id')->comment('使用者id');
            $table->text('msg')->comment('訊息');
            $table->decimal('points', 32, 8)->comment('點數(可負數)');
            $table->text('msg_time')->comment('紀錄建立時間');
            $table->text('belongs_time')->comment('點數屬於何時間(避免訂單取消回點影響有效期間判斷)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('points_record');
    }
};
