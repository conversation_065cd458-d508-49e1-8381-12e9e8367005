<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('member_article_report', function (Blueprint $table) {
            $table->comment('會員文章-檢舉紀錄');
            $table->integer('id', true);
            $table->char('ip', 64)->comment('檢舉人ip');
            $table->integer('article_id')->default(0)->comment('對應文章id');
            $table->text('note')->default('')->comment('檢舉內容');
            $table->char('create_time', 10)->default('')->comment('建立時間(timestamp)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('member_article_report');
    }
};
