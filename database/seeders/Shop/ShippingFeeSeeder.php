<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ShippingFeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=shipping_fee --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'name' => '宅配',
                'price' => 100,
                'free_rule' => 499,
                'order_id' => 4,
                'online' => 1,
                'sys_status' => 1,
            ],
            [
                'id' => 7,
                'name' => '萊爾富取貨',
                'price' => 90,
                'free_rule' => 499,
                'order_id' => 3,
                'online' => 1,
                'sys_status' => 1,
            ],
            [
                'id' => 8,
                'name' => '7-11取貨',
                'price' => 60,
                'free_rule' => 499,
                'order_id' => 2,
                'online' => 1,
                'sys_status' => 1,
            ],
            [
                'id' => 9,
                'name' => '到店取貨',
                'price' => 0,
                'free_rule' => 0,
                'order_id' => 1,
                'online' => 1,
                'sys_status' => 1,
            ]
        ];
        DB::table('shipping_fee')->truncate();
        DB::table("shipping_fee")->insert($dataTables);
    }
}
