<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('member_group', function (Blueprint $table) {
            $table->integer('id', true);
            $table->text('name')->nullable();
            $table->text('data')->nullable();
            $table->text('member_group')->nullable();
            $table->integer('total')->nullable();
            $table->integer('creat_time')->nullable();
            $table->integer('update_time')->nullable();
            $table->integer('status')->nullable()->default(1);

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('member_group');
    }
};
