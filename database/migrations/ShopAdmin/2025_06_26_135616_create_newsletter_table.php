<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('newsletter', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('number', 11)->nullable();
            $table->text('title');
            $table->text('msg')->nullable();
            $table->integer('create_time')->nullable();
            $table->integer('update_time')->nullable();
            $table->integer('send_time')->nullable()->default(0);
            $table->integer('total')->nullable()->default(0);
            $table->integer('status')->nullable()->default(1);
            $table->text('msgid')->nullable();
            $table->integer('msg_status')->default(0)->comment('狀態 0.未安排 1.排程中 2.取消 3.寄送中 4.已完成');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('newsletter');
    }
};
