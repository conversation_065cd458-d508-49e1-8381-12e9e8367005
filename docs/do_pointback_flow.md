# OrderHelper::do_pointback() 積分回饋流程說明

## 主要功能

針對一組訂單 ID，進行積分分潤與回饋處理，確保每張訂單的分潤、點數、會員升級等獎勵正確發放。

## 流程步驟

1. **訂單檢查**

    - 逐筆檢查訂單是否存在、已完成、已付款、未重複回饋、營運者/講師/中心會員皆已設定。
    - 若有任一條件不符，直接丟出例外。

2. **標記回饋處理時間**

    - 將訂單及其商品標記為已處理回饋，避免重複發放。

3. **初始化分潤對象**

    - 依訂單資訊，初始化購買者、推薦人、營運者、講師、中心、上層中心等分潤對象。

4. **設定回饋參數**

    - 設定購買者、推薦人、訂單編號等資訊，作為分潤依據。

5. **商品分潤與點數計算**

    - 只處理「商品」類型（排除運費等）。
    - 投資商品：累積投資金額。
    - 消費商品：
        - 供應商回饋。
        - 購買者獲得消費圓滿點數。
        - 若有課程升級，調整會員級別與課程進度。
        - 分潤金額依 bonus_model_id 分配給推薦人、營運者、講師、中心、上層中心、月分紅帳戶、系統帳戶。
        - 若商品有廣告設定，分潤改為總部消費回饋，依合夥人權重分配。

6. **會員級別升級**

    - 根據消費後的 GV 值，自動調整購買者與推薦人的會員級別。

7. **發放分潤**
    - 統計所有分潤結果，並實際發放積分、點數、升級等獎勵。

---

## Mermaid 流程圖

```mermaid
flowchart TD
    A[開始] --> B[檢查訂單狀態]
    B -->|通過| C[標記回饋處理時間]
    C --> D[初始化分潤對象]
    D --> E[設定回饋參數]
    E --> F[逐商品分潤與點數計算]
    F --> G[會員級別升級]
    G --> H[發放分潤]
    H --> I[結束]
    B --|不通過| X[丟出例外，結束]
```

---

## 分潤分配規則細節

### 1. 投資商品

-   只累積購買者的「投資金額」，不進行分潤分配。

### 2. 消費商品（無廣告）

-   依據商品的 bonus_model_id，將「分享 CV 金額」分配給下列對象：
    -   購買者推薦人（推廣獎勵）
    -   推薦人的推薦人（合夥平級獎勵）
    -   營運者（營運獎勵）
    -   講師（講師獎勵）
    -   中心、上層中心（中心獎勵，依級差占比拆分）
    -   月分紅帳戶（分紅獎勵）
    -   系統帳戶（未分配部分回收）

### 3. 消費商品（有廣告）

-   「分享 CV 金額」依合夥人權重分配給所有有效合夥人。

### 4. 供應商回饋

-   商品供應商可獲得「供應商回饋」金額。

### 5. 會員升級

-   購買者與推薦人依消費後 GV 值自動升級會員級別。

---

## 分潤分配公式

### 消費商品（無廣告）

-   計算「分享 CV 金額」：  
    `分享CV金額 = BonusHelper::count_share_cv(校正CV金額)`
-   各分潤對象分配如下：
    -   **購買者推薦人（推廣獎勵）**  
        `分配金額 = BonusHelper::count_available_cv(bonus_model_id, 1, 推薦人分享CV)`
    -   **推薦人的推薦人（合夥平級獎勵）**  
        `分配金額 = BonusHelper::count_available_cv(bonus_model_id, 2, 分享CV)`
    -   **營運者（營運獎勵）**  
        `分配金額 = BonusHelper::count_available_cv(bonus_model_id, 3, 分享CV)`
    -   **講師（講師獎勵）**  
        `分配金額 = BonusHelper::count_available_cv(bonus_model_id, 4, 分享CV)`
    -   **中心、上層中心（中心獎勵）**  
        `分配金額 = BonusHelper::count_available_cv(bonus_model_id, 5, 分享CV)`  
        依級差占比拆分：  
        `本層中心 = (分配金額 * center_lower_weight / total_weight)`  
        `上層中心 = (分配金額 * center_upper_weight / total_weight)`
    -   **月分紅帳戶**  
        `分配金額 = BonusHelper::count_available_cv(bonus_model_id, 6, 分享CV)`
    -   **系統帳戶**  
        `分配金額 = 分享CV - 已分配總額`

### 消費商品（有廣告）

-   分享 CV 金額依所有有效合夥人權重分配：  
    `合夥人分配金額 = (分享CV金額 * 合夥人權重 / 合夥人總權重)`

---

## 分潤分配範例

假設一筆消費商品（無廣告）訂單，校正 CV 金額為 1,000，bonus_model_id 設定如下：

| 分潤對象       | 分潤比例 |
| -------------- | -------- |
| 推薦人         | 20%      |
| 推薦人之推薦人 | 10%      |
| 營運者         | 10%      |
| 講師           | 10%      |
| 中心/上層中心  | 20%      |
| 月分紅帳戶     | 10%      |
| 系統帳戶       | 剩餘 20% |

-   分享 CV 金額 = 1,000
-   各分潤金額如下：

| 分潤對象       | 分配金額 |
| -------------- | -------- |
| 推薦人         | 200      |
| 推薦人之推薦人 | 100      |
| 營運者         | 100      |
| 講師           | 100      |
| 中心/上層中心  | 200      |
| 月分紅帳戶     | 100      |
| 系統帳戶       | 200      |

-   中心/上層中心再依級差占比拆分，例如本層中心 60%，上層中心 40%：
    -   本層中心：200 × 60% = 120
    -   上層中心：200 × 40% = 80

---

## 主要呼叫之 BonusHelper 方法

-   `init_user_set`：初始化分潤對象
-   `set_buyer_id`、`set_buyer_topline_id`、`set_orderform_id`、`set_order_number`：設定分潤參數
-   `get_count_cv`：取得商品校正 CV 金額
-   `add_total_invest`：投資金額累積
-   `add_supplier_bonus`：供應商回饋
-   `add_limit_consumption`：消費圓滿點數
-   `set_final_vip_type`：會員級別升級
-   `add_pi_pool`、`count_share_cv`、`count_share_cv_vip_type`、`count_available_cv`、`add_available_cv`、`add_available_cv_center`：分潤計算與分配
-   `send_by_cal`：發放分潤
