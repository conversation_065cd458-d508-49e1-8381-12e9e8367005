<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('message_list', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('number', 11)->nullable();
            $table->text('title')->nullable();
            $table->text('msg')->nullable();
            $table->integer('create_time')->nullable();
            $table->integer('update_time')->nullable();
            $table->integer('send_time')->nullable();
            $table->integer('total')->nullable()->default(0);
            $table->integer('status')->nullable()->default(1);
            $table->text('msgid')->nullable();
            $table->string('msg_status', 10)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('message_list');
    }
};
