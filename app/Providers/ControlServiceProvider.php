<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\pattern\HelperService;
use Database\Seeders\ShopAdmin\ExcelSeeder;

class ControlServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        try {
            $excel_function = HelperService::get_excel_function();
            foreach ($excel_function as $key => $value) {
                config(['control.' . $key => $value]);
            }

            $function_result_current = HelperService::get_frontend_user_use_function();
            config(['control.show_list_current' => $function_result_current['show_list']]);
            config(['control.show_list_group_current' => $function_result_current['show_list_group']]);
            config(['control.close_function_current' => $function_result_current['close_function']]);
            config(['control.close_desk_current' => $function_result_current['close_desk']]);
            config(['control.close_desk_admin_current' => $function_result_current['close_desk_admin']]);
        } catch (\Exception) {
            // 當資料庫查詢失敗時（如 migration 期間），使用預設值
            $this->setDefaultControlConfig();
        }
    }

    /**
     * 設定預設的控制參數（當資料庫不可用時使用）
     */
    private function setDefaultControlConfig()
    {
        // 從 ExcelSeeder 取得預設資料
        $excel_function = $this->getDefaultExcelFunction();
        foreach ($excel_function as $key => $value) {
            config(['control.' . $key => $value]);
        }

        // 設定預設的前端功能參數
        $default_function_result = $this->getDefaultFrontendFunction();
        config(['control.show_list_current' => $default_function_result['show_list']]);
        config(['control.show_list_group_current' => $default_function_result['show_list_group']]);
        config(['control.close_function_current' => $default_function_result['close_function']]);
        config(['control.close_desk_current' => $default_function_result['close_desk']]);
        config(['control.close_desk_admin_current' => $default_function_result['close_desk_admin']]);
    }

    /**
     * 取得預設的 Excel 功能設定（基於 ExcelSeeder 資料）
     */
    private function getDefaultExcelFunction()
    {
        $excel_data = ExcelSeeder::getExcelData();

        // 將 seeder 資料轉換為索引陣列格式（模擬資料庫查詢結果）
        $excel = [];
        foreach ($excel_data as $item) {
            $excel[$item['id'] - 1] = $item; // 轉換為 0-based 索引
        }

        $excel_function = [];
        $excel_function['control_auto_logout'] = $excel[4]['value1'];           /* 自動登出 */
        $excel_function['control_pre_buy'] = $excel[0]['value1'];               /* 商品可否設定超額購買 */
        $excel_function['control_card_pay'] = $excel[6]['value1'];              /* 商品可否設定刷卡 */
        $excel_function['control_product_paying'] = $excel[20]['value1'];       /* 商品可否設定付款方法 */
        $excel_function['control_register'] = $excel[13]['value1'];             /* 是否啟用報名功能 */
        $excel_function['control_product_shipping'] = $excel[14]['value1'];     /* 商品可否設定運法 */
        $excel_function['control_time_limit_prod'] = $excel[8]['value1'];       /* 首頁是否顯示限時搶購 */
        $excel_function['control_index_edm'] = $excel[10]['value1'];            /* 首頁是否顯示EDM */
        $excel_function['control_sepc_price'] = $excel[9]['value1'];            /* 是否使用特價商品(不限數量標籤) */
        $excel_function['control_copy_prod'] = $excel[11]['value1'];            /* 後台複製商品 */
        $excel_function['control_third_party_payment'] = $excel[15]['value1'];  /* 第三方金流 */
        $excel_function['control_third_party_logistics'] = $excel[16]['value1']; /* 第三方物流 */
        $excel_function['control_third_party_invoice'] = $excel[17]['value1'];  /* 第三方發票 */
        $excel_function['control_FirstBuyDiscount'] = $excel[18]['value1'];     /* 首購優惠 */
        $excel_function['control_VipDiscount'] = $excel[19]['value1'];          /* VIP等級 */
        $excel_function['control_platform'] = $excel[21]['value1'];             /* 是否平台化 */
        $excel_function['control_img_quantity'] = $excel[2]['value1'];          /* 商品照片數量 */
        $excel_function['control_prod_type_layer'] = $excel[3]['value1'];       /* 商品階層品項 */
        $excel_function['control_upload_film'] = $excel[5]['value1'];           /* 商品圖是否可以放影片 */
        $excel_function['control_prod_edm'] = $excel[7]['value1'];              /* 商品是否顯示EDM */
        $excel_function['control_social_share'] = $excel[12]['value1'];         /* 商品是否可單一商品社群分享 */
        $excel_function['control_down_line'] = $excel[22]['value1'];            /* 是否使用招募會員 */

        $excel_function['invoice_style_text'] = [                               /* 發票開立方式選項 */
            "1" => '個人實體紙本發票',
            "2" => '個人電子郵件寄送發票',
            "3" => '個人共通性載具',
            "4" => '公司戶發票',
            "5" => '捐贈',
        ];

        return $excel_function;
    }

    /**
     * 取得預設的前端功能設定（當資料庫不可用時使用）
     */
    private function getDefaultFrontendFunction()
    {
        // 提供基本的預設值，避免系統崩潰
        return [
            'show_list' => [],
            'show_list_group' => [],
            'close_function' => [],
            'close_desk' => [],
            'close_desk_admin' => [],
        ];
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
