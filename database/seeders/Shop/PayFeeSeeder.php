<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PayFeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=pay_fee --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'name' => '貨到付款',
                'order_id' => 0,
                'online' => 1,
                'sys_status' => 1,
            ],
            [
                'id' => 2,
                'name' => 'ATM轉帳\\匯款',
                'order_id' => 1,
                'online' => 1,
                'sys_status' => 1,
            ],
            [
                'id' => 3,
                'name' => '線上刷卡',
                'order_id' => 2,
                'online' => 1,
                'sys_status' => 1,
            ],
            [
                'id' => 4,
                'name' => '分期付款',
                'order_id' => 3,
                'online' => 1,
                'sys_status' => 1,
            ],
            [
                'id' => 5,
                'name' => 'LinePay',
                'order_id' => 4,
                'online' => 1,
                'sys_status' => 0,
            ]
        ];
        DB::table('pay_fee')->truncate();
        DB::table("pay_fee")->insert($dataTables);
    }
}
