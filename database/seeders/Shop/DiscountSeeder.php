<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DiscountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=discount --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'distributor_id' => 0,
                'name' => '1折',
                'number' => '0.1',
            ],
            [
                'id' => 2,
                'distributor_id' => 0,
                'name' => '2折',
                'number' => '0.2',
            ],
            [
                'id' => 3,
                'distributor_id' => 0,
                'name' => '3折',
                'number' => '0.3',
            ],
            [
                'id' => 4,
                'distributor_id' => 0,
                'name' => '5折',
                'number' => '0.5',
            ],
            [
                'id' => 5,
                'distributor_id' => 0,
                'name' => '7折',
                'number' => '0.7',
            ],
            [
                'id' => 6,
                'distributor_id' => 0,
                'name' => '9折',
                'number' => '0.9',
            ],
            [
                'id' => 12,
                'distributor_id' => 0,
                'name' => '1折:商品狀況良好',
                'number' => '0.1',
            ]
        ];
        DB::table('discount')->truncate();
        DB::table("discount")->insert($dataTables);
    }
}
