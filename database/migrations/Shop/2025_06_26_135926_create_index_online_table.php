<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('index_online', function (Blueprint $table) {
            $table->tinyInteger('id', true);
            $table->tinyInteger('block1');
            $table->tinyInteger('block2');
            $table->tinyInteger('block3');
            $table->tinyInteger('block4');
            $table->tinyInteger('block5');
            $table->tinyInteger('block6');
            $table->tinyInteger('block7');
            $table->tinyInteger('block8');
            $table->tinyInteger('block9');
            $table->tinyInteger('block10');
            $table->tinyInteger('block11');
            $table->tinyInteger('block12')->default(1);
            $table->integer('block13');
            $table->tinyInteger('block7_new');
            $table->tinyInteger('block10_new');
            $table->tinyInteger('block_spe_price');
            $table->tinyInteger('block_edm')->default(0)->comment('EDM');
            $table->tinyInteger('block_iframe')->default(1)->comment('嵌入區開關');
            $table->tinyInteger('block_news')->default(1)->comment('最新消息開關');
            $table->boolean('product_nav_total')->default(true)->comment('總商品選單');
            $table->boolean('nav_other')->default(true)->comment('顯示其他選單');
            $table->boolean('nav_other_footer')->default(true)->comment('顯示其他選單頁尾');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('index_online');
    }
};
