<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('consumption_pay_record', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('user_id')->comment('會員id');
            $table->integer('price')->comment('金額');
            $table->text('datetime')->nullable()->comment('付款時間');
            $table->boolean('audit')->default(false)->comment('審核狀態 0.未通過 1.通過');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('consumption_pay_record');
    }
};
