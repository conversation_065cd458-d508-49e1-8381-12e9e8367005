<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupon', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->string('pic', 256)->nullable()->default('');
            $table->text('title');
            $table->string('number', 32);
            $table->integer('price')->default(0);
            $table->integer('limit_num')->nullable()->default(5)->comment('領取上限');
            $table->boolean('type')->comment('1 => 實體卷 0 => 虛擬卷');
            $table->string('ps', 1024)->nullable()->default('');
            $table->string('content', 1024)->nullable()->default('');
            $table->integer('start');
            $table->integer('end');
            $table->integer('discount');
            $table->integer('coupon_condition');
            $table->tinyInteger('transfer')->default(0);
            $table->integer('num');
            $table->integer('area');
            $table->integer('area_id');
            $table->tinyInteger('text1_online')->nullable()->default(0);
            $table->string('text1', 2048)->nullable();
            $table->tinyInteger('text2_online')->nullable()->default(0);
            $table->string('text2', 2048)->nullable();
            $table->tinyInteger('text3_online')->nullable()->default(0);
            $table->string('text3', 2048)->nullable();
            $table->tinyInteger('text4_online')->nullable()->default(0);
            $table->string('text4', 2048)->nullable();
            $table->tinyInteger('online')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupon');
    }
};
