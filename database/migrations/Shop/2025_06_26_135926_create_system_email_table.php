<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('system_email', function (Blueprint $table) {
            $table->integer('id')->primary();
            $table->text('signup_complete')->nullable()->comment('註冊成功信');
            $table->text('contact_complete')->nullable()->comment('回函成功信');
            $table->text('order_complete')->nullable()->comment('訂購成功信');
            $table->text('forget_password')->nullable()->comment('忘記密碼信');
            $table->text('order_cancel')->nullable()->comment('取消訂單信');
            $table->text('product_qa')->nullable()->comment('商品詢問信');
            $table->text('act_remind')->nullable()->comment('活動提醒信');
            $table->text('act_cancel')->nullable()->comment('活動取消信');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('system_email');
    }
};
