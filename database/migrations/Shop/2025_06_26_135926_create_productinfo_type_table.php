<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('productinfo_type', function (Blueprint $table) {
            $table->comment('商品規格詳細資訊');
            $table->integer('id', true);
            $table->integer('product_id');
            $table->integer('num')->comment('線上可購買量');
            $table->integer('limit_num')->default(10)->comment('庫存警示量');
            $table->integer('price');
            $table->integer('count');
            $table->decimal('price_cv', 26)->default(0)->comment('CV金額');
            $table->decimal('price_supplier', 26)->default(0)->comment('供應商結算金額(美金)');
            $table->text('title')->nullable();
            $table->integer('pic_index')->default(1)->comment('對應商品圖片第幾張');
            $table->string('position', 10)->nullable();
            $table->integer('discount_id')->nullable()->default(0);
            $table->integer('order_id')->default(0)->comment('排序');
            $table->boolean('online')->default(true)->comment('狀態 1.啟用 0.假刪除');
            $table->text('start_time')->default('')->comment('報名開始時間');
            $table->text('end_time')->default('')->comment('報名結束時間');
            $table->text('act_time')->default('')->comment('活動開始時間');
            $table->text('act_time_end')->default('')->comment('活動結束時間');
            $table->text('act_remind_time')->default('')->comment('活動提醒時間');
            $table->boolean('closed')->default(false)->comment('停止開課 0.否 1.是');
            $table->text('closed_date')->nullable()->comment('取消課程日期');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('productinfo_type');
    }
};
