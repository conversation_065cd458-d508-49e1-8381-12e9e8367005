<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCategoryTypeToProductinfo extends Migration
{
    public function up()
    {
        Schema::table('productinfo', function (Blueprint $table) {
            $table->tinyInteger('category_type')->default(0)->comment('0=商城, 1=課程,..後續有需要再加');
        });
    }

    public function down()
    {
        Schema::table('productinfo', function (Blueprint $table) {
            $table->dropColumn('category_type');
        });
    }
}
