<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('basic_auth', function (Blueprint $table) {
            $table->comment('API basic auth帳密列表');
            $table->integer('id', true);
            $table->char('username', 128)->comment('帳號');
            $table->char('password', 128)->comment('密碼');
            $table->char('note', 128)->comment('備註');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('basic_auth');
    }
};
