<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('bonus_setting', function (Blueprint $table) {
            $table->comment('回饋設定');
            $table->integer('id', true);
            $table->char('value', 128)->comment('值');
            $table->char('note', 128)->comment('說明');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('bonus_setting');
    }
};
