<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('index_excel', function (Blueprint $table) {
            $table->integer('id', true);
            $table->text('data1')->nullable()->comment('一般存圖');
            $table->text('data2')->nullable()->comment('一般URL');
            $table->text('data3')->nullable()->comment('一般文字');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('index_excel');
    }
};
