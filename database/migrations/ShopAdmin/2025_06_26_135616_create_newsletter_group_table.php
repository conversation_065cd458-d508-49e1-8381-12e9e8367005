<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('newsletter_group', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('newsletter_id')->comment('對應newsletter id');
            $table->integer('group')->comment('哪一批次');
            $table->integer('schedule_time')->comment('排程日期');
            $table->integer('create_time')->comment('創建日期');
            $table->text('title')->nullable()->comment('標題');
            $table->text('msg')->nullable()->comment('信件原始內容');
            $table->text('do_send_group')->nullable()->comment('發送群組(json list)');

            $table->unique(['newsletter_id', 'group'], 'newsletter_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('newsletter_group');
    }
};
