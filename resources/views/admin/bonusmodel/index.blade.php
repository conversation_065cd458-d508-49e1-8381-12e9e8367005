@extends('admin.Public.aside')
@section('title')回饋模組 - J參數設定@endsection

@section('css')
@endsection

@section('content')
<div id="content">
    <ul id="title" class="brand-menu">
        <li><a href="###">J參數設定</a></li>
        <li><a href="###">回饋模組</a></li>
    </ul>
    <div class="frame">
        <a href="{{ url('admin/bonusmodel/create') }}" class="btn clearbtn mr-3"><span class="bi bi-plus-lg add" ></span> 新增</a>
    </div>
    <div class="edit_form">
        <table class="table table-rwd" style="min-width:575px; width:575px;">
            <thead>
                <tr>
                    <th style="width: 75px;">
                        <!-- <input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=productinfoCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="cursor:pointer;"> -->
                        序號
                    </th>
                    <!-- <th style="width: 150px;">模組ID</th> -->
                    <th style="width: 300px;">{{Lang::get('名稱')}}</th>
                    <th style="width:200px;">{{Lang::get('操作')}}</th>
                </tr>
            </thead>
            <tbody>
                <tr></tr>
                <tr v-for="(vo, vo_idx) in items" :id="'items_' +vo.id">
                    <td>
                        <!-- <input type="checkbox" class="productinfoCheckbox" :alt="vo.id"> -->
                        <span v-text="vo_idx+1"></span>
                    </td>
                    <!-- <td><span v-text="vo.id"></span></td> -->
                    <td>
                        <a :href="'{{ url('admin/bonusmodel/edit') }}/' + vo.id">
                            <span v-text="vo.name"></span>
                        </a>
                    </td>
                    <td>
                        <button class="btn btn-danger pt-1 pb-1 pl-2 pr-2" @click="del_item(vo_idx)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>


</div>
@endsection
@section('ownJS')
    <script>
        var content_data = {
            items:[],
        };
        var contentVM = new Vue({
            el:'#content',
            data: content_data,
            async created(){
                $('#block_block').show()
                var resp = await this.load_data();
                console.log('R', resp);

                this.items = resp.db_data;
                $('#block_block').hide();
            },
            methods:{
                load_data(){
                    return $.ajax({
                        type: "GET",
                        headers: {
                            'X-CSRF-Token': csrf_token
                        },
                        dataType: "json",
                        data:{
                        },
                        url: "{{url('Bonusmodel/get_data')}}",
                    });
                },
                async del_item(idx){
                    if(idx<0 || idx>=this.items.length){ return; }
                    if(!confirm("確定刪除嗎?")){ return; }
                    var target_id = this.items[idx].id;
                    try {
                        var resp = await $.ajax({
                            type: "POST",
                            headers: {
                                'X-CSRF-Token': csrf_token
                            },
                            dataType: "json",
                            data:{
                                id: target_id,
                            },
                            url: "{{url('Bonusmodel/delete_data')}}",
                        });
                        if(resp.code==1){
                            this.items.splice(idx, 1);
                            Vue.toasted.show(resp.msg, vt_success_obj);
                        }else{
                            Vue.toasted.show(resp.msg, vt_error_obj);
                        }
                    } catch (error) {
                        // console.log(error);
                        Vue.toasted.show(error.statusText, vt_error_obj);
                    }
                    $('#block_block').hide();
                }
            },
        });
    </script>
@endsection
