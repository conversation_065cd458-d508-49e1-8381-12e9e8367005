<?php

namespace Database\Seeders\ShopAdmin;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ExcelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = self::getExcelData();
        DB::connection('main_db')->table('excel')->truncate();
        DB::connection('main_db')->table('excel')->insert($data);
    }


    /**
     * 為什麼要建這個方法？
     * 你可以去看看
     *      app/Providers/ControlServiceProvider.php
     *      app/Services/pattern/HelperService.php
     * 看看他們幹了什麼好事..我特別註解起來
     */
    public static function getExcelData()
    {
        return [
            ['id' => 1, 'value1' => 0, 'value2' => '是否啟用超額購買功能 1.可 0.不可'],
            ['id' => 2, 'value1' => 99, 'value2' => '12-31'],
            ['id' => 3, 'value1' => 20, 'value2' => '商品圖片上限'],
            ['id' => 4, 'value1' => 1, 'value2' => '商品階層品項 1.可 0.不可'],
            ['id' => 5, 'value1' => 172800, 'value2' => '自動登出'],
            ['id' => 6, 'value1' => 0, 'value2' => '商品圖是否可以放影片 0.不可  1.可'],
            ['id' => 7, 'value1' => 1, 'value2' => '是否可以指定商品刷卡 0.不可  1.可'],
            ['id' => 8, 'value1' => 0, 'value2' => '商品插入EDM 0.不可 1.可'],
            ['id' => 9, 'value1' => 0, 'value2' => '首頁顯示限時搶購 0.不可 1.可'],
            ['id' => 10, 'value1' => 1, 'value2' => '使用特價商品(無上限商品標籤) 0.不可 1.可'],
            ['id' => 11, 'value1' => 0, 'value2' => '首頁插入EDM 0.不可 1.可'],
            ['id' => 12, 'value1' => 1, 'value2' => '後台複製商品 0.不可 1.可'],
            ['id' => 13, 'value1' => 1, 'value2' => '單一商品社群分享 0.不可 1.可'],
            ['id' => 14, 'value1' => 0, 'value2' => '使用商品報名 0.不可 1.可'],
            ['id' => 15, 'value1' => 1, 'value2' => '運費關聯商品 0.不可 1.可'],
            ['id' => 16, 'value1' => 1, 'value2' => '第三方金流 0.不可 1.可'],
            ['id' => 17, 'value1' => 0, 'value2' => '第三方物流 0.不可 1.可'],
            ['id' => 18, 'value1' => 0, 'value2' => '第三方發票 0.不可 1.可'],
            ['id' => 19, 'value1' => 0, 'value2' => '首購優惠 0.不可 1.可'],
            ['id' => 20, 'value1' => 1, 'value2' => 'VIP等級優惠 0.不可 1.可'],
            ['id' => 21, 'value1' => 1, 'value2' => '是否可以指定商品付款方式 0.不可 1.可'],
            ['id' => 22, 'value1' => 1, 'value2' => '是否平台化 0.否 1.是'],
            ['id' => 23, 'value1' => 1, 'value2' => '是否使用招募會員 0.否 1.是'],
        ];
    }
}
