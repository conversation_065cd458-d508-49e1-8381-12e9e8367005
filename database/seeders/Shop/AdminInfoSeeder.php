<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AdminInfoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=admin_info --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'favicon' => '/upload/phpKMw0M3',
                'customer_name' => '天脈能量購物車',
                'system_name' => '傳訊光科技股份有限公司',
                'marketing_name' => '和承國際整合行銷',
                'url' => 'http://bigwell.com.tw/',
                'tel' => '02-2738-6266',
                'email' => '<EMAIL>',
                'address' => '臺北市信義區基隆路２段189號16樓之8',
                'customer_logo' => '/upload/phpqUoT9a',
                'system_logo' => '/upload/06/c8f380eb5d209a9c213e03380c0212.png',
                'marketing_logo' => '',
                'success_logo' => NULL,
                'error_logo' => NULL,
            ]
        ];

        DB::table('admin_info')->truncate();
        DB::table("admin_info")->insert($dataTables);
    }
}
