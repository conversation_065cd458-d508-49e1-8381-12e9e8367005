<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('vip_type_relation', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id')->comment('會員id');
            $table->integer('vip_type_id')->comment('vip等級id');
            $table->text('datetime')->nullable()->comment('時間');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('vip_type_relation');
    }
};
