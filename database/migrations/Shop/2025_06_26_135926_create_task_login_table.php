<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('task_login', function (Blueprint $table) {
            $table->comment('任務牆-會員登入紀錄');
            $table->integer('id', true);
            $table->integer('user_id')->comment('會員id');
            $table->char('time', 10)->default('')->comment('登入時間(timestamp)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('task_login');
    }
};
