<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('frontend_data_name', function (Blueprint $table) {
            $table->integer('id', true);
            $table->text('name')->nullable();
            $table->text('url')->nullable();
            $table->text('show_type')->nullable()->comment('後台顯示分類');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('frontend_data_name');
    }
};
