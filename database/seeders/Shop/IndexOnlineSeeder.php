<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class IndexOnlineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=index_online --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'block1' => 0,
                'block2' => 0,
                'block3' => 0,
                'block4' => 0,
                'block5' => 0,
                'block6' => 0,
                'block7' => 1,
                'block8' => 0,
                'block9' => 0,
                'block10' => 1,
                'block11' => 0,
                'block12' => 0,
                'block13' => 0,
                'block7_new' => 0,
                'block10_new' => 0,
                'block_spe_price' => 0,
                'block_edm' => 1,
                'block_iframe' => 1,
                'block_news' => 0,
                'product_nav_total' => 0,
                'nav_other' => 0,
                'nav_other_footer' => 0,
            ]
        ];
        DB::table('index_online')->truncate();
        DB::table("index_online")->insert($dataTables);
    }
}
