<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('dividend_month_record', function (Blueprint $table) {
            $table->comment('月分紅認列紀錄，若以月分紅期數[50,30,20]來看，3月的訂單若處理回饋時，將建立3筆認列紀錄月份分別是3、4、5，實際分配月份分別是4、5、6。');
            $table->integer('id', true);
            $table->integer('orderform_id')->comment('對應訂單id');
            $table->decimal('num', 32, 8)->default(0)->comment('增值積分數量');
            $table->char('datetime', 10)->comment('認列月份(timestamp)(次月再分配)');
            $table->boolean('used')->default(false)->comment('已使用 0.否 1.是');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('dividend_month_record');
    }
};
