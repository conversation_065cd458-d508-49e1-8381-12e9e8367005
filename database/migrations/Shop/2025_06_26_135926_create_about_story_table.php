<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('about_story', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('image_left_top', 128);
            $table->string('image_right_top', 128);
            $table->string('image_right_bottom', 128);
            $table->text('content')->nullable();
            $table->text('mapurl');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('about_story');
    }
};
