<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WanpayDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=wanpay_data --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'pay_way' => '[
 {"show_name":"現場付款", "channel":"現場付款"},
 {"show_name":"ATM付款", "channel":"ATM付款"}
]',
                'wanpay_way' => '[]',
                'shop_no' => NULL,
                'wanpay_key' => NULL,
                'wanpay_content' => NULL,
                'regist_time' => NULL,
            ]
        ];
        DB::table('wanpay_data')->truncate();
        DB::table("wanpay_data")->insert($dataTables);
    }
}
