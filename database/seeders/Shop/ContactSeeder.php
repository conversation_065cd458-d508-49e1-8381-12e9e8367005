<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=contact --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'distributor_id' => 0,
                'contact_type' => '行銷活動,其他',
            ]
        ];
        DB::table('contact')->truncate();
        DB::table("contact")->insert($dataTables);
    }
}
