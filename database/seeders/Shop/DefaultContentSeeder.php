<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DefaultContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=default_content --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'distributor_id' => 0,
                'default_type' => 'productinfo',
                'text1' => '<div style="text-align:center;">
	<p style="text-align:center;">
		12312321
	</p>
</div>',
                'text2' => 21321323,
                'text3' => '',
                'text4' => '<br />
<span style="font-size:18px;">刷卡連結</span><br />
<a href="https://n.gomypay.asia/NowPay.aspx?CustomerNo=96C23A6BB7D3596C3B51C556967440EE" target="_blank"><span style="font-size:18px;">https://n.gomypay.asia/NowPay.aspx?CustomerNo=96C23A6BB7D3596C3B51C556967440EE</span></a><br />
<br />
<span style="font-size:18px;">匯款：</span><br />
<span style="font-size:18px;">中國信託(822]江翠分行</span><br />
<span style="font-size:18px;">576540390995</span><br />
<span style="font-size:18px;">天脈能量有限公司</span><br />
<br />
<span style="font-size:18px;">(請截圖保存付款紀錄給小幫手]</span><br />
<br />',
                'text5' => NULL,
                'note' => '商品的預設內容',
            ],
            [
                'id' => 2,
                'distributor_id' => 0,
                'default_type' => 'coupon',
                'text1' => '',
                'text2' => '',
                'text3' => '',
                'text4' => NULL,
                'text5' => NULL,
                'note' => '優惠券的預設內容',
            ]
        ];
        DB::table('default_content')->truncate();
        DB::table("default_content")->insert($dataTables);
    }
}
