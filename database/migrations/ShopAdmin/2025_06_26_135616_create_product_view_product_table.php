<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('product_view_product', function (Blueprint $table) {
            $table->integer('view_prod_id', true);
            $table->integer('lang_id')->comment('語言版id');
            $table->integer('view_id');
            $table->integer('prod_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('product_view_product');
    }
};
