<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->table('bonus_model', function (Blueprint $table) {
            //先更改 comment
            $table->float('normal_operation')->default(0)->comment('行政廣告奬勵(ex:5)(使用時要除100)')->change();
            $table->float('partner_operation')->default(0)->comment('合夥設定:行政廣告奬勵(ex:5)(使用時要除100)')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->table('bonus_model', function (Blueprint $table) {
            $table->float('normal_operation')->default(0)->comment('行政廣告奬勵(ex:5)(使用時要除100)')->change();
            $table->float('partner_operation')->default(0)->comment('合夥設定:行政廣告奬勵(ex:5)(使用時要除100)')->change();
        });
    }
};
