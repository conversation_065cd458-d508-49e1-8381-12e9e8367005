
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->table('bonus_model', function (Blueprint $table) {
            // 1. 改名 normal_operation -> normal_marketing_dept
            $table->renameColumn('normal_operation', 'normal_marketing_dept'); //再更改欄位名..

            // 5. 新增 normal_center_founder
            $table->float('normal_center_founder')->default(0)->comment('中心發起人奬勵(ex:2)(使用時要除100)')->after('normal_operation');

            // 4. 新增 normal_center_director
            $table->float('normal_center_director')->default(0)->comment('中心總監奬勵(ex:2)(使用時要除100)')->after('normal_operation');

            // 3. 新增 normal_executive_director
            $table->float('normal_executive_director')->default(0)->comment('大總監奬勵(ex:1)(使用時要除100)')->after('normal_operation');

            // 2. 新增 normal_sales_dept
            $table->float('normal_sales_dept')->default(0)->comment('業務部門奬勵(ex:7)(使用時要除100)')->after('normal_operation');
            //-----------------------------

            // 1. 改名 partner_operation -> partner_marketing_dept
            $table->renameColumn('partner_operation', 'partner_marketing_dept');

            // 5. 新增 partner_center_founder
            $table->float('partner_center_founder')->default(0)->comment('合夥設定:中心發起人奬勵(ex:2)(使用時要除100)')->after('partner_operation');

            // 4. 新增 partner_center_director
            $table->float('partner_center_director')->default(0)->comment('合夥設定:中心總監奬勵(ex:2)(使用時要除100)')->after('partner_operation');

            // 3. 新增 partner_executive_director
            $table->float('partner_executive_director')->default(0)->comment('合夥設定:大總監奬勵(ex:1)(使用時要除100)')->after('partner_operation');

            // 2. 新增 partner_sales_dept
            $table->float('partner_sales_dept')->default(0)->comment('合夥設定:業務部門奬勵(ex:7)(使用時要除100)')->after('partner_operation');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->table('bonus_model', function (Blueprint $table) {
            // 還原欄位名稱
            $table->renameColumn('normal_marketing_dept', 'normal_operation');
            $table->renameColumn('partner_marketing_dept', 'partner_operation');

            // 刪除新增欄位
            $table->dropColumn([
                'normal_center_founder',
                'normal_center_director',
                'normal_executive_director',
                'normal_sales_dept',
                'partner_center_founder',
                'partner_center_director',
                'partner_executive_director',
                'partner_sales_dept'
            ]);
        });
    }
};
