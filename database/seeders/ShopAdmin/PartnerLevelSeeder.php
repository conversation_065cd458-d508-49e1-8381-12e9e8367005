<?php

namespace Database\Seeders\ShopAdmin;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PartnerLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 為了確保資料的一致性，建議在插入前先清空資料表
        DB::connection('main_db')->table('partner_level')->truncate();

        DB::connection('main_db')->table('partner_level')->insert([
            ['id' => 1, 'name' => '微合夥人', 'ratio' => 1.25, 'contribution' => 132, 'partner_bonus_ratio' => 5, 'orderform_ad_weight' => 1],
            ['id' => 2, 'name' => '創業合夥人', 'ratio' => 1.5, 'contribution' => 400, 'partner_bonus_ratio' => 10, 'orderform_ad_weight' => 2],
            ['id' => 3, 'name' => '準合夥人', 'ratio' => 1.75, 'contribution' => 1320, 'partner_bonus_ratio' => 15, 'orderform_ad_weight' => 4],
            ['id' => 4, 'name' => '初級合夥人', 'ratio' => 2, 'contribution' => 4400, 'partner_bonus_ratio' => 20, 'orderform_ad_weight' => 16],
            ['id' => 5, 'name' => '高級合夥人', 'ratio' => 2.25, 'contribution' => 13200, 'partner_bonus_ratio' => 40, 'orderform_ad_weight' => 16],
            ['id' => 6, 'name' => '區級合夥人', 'ratio' => 2.5, 'contribution' => 44000, 'partner_bonus_ratio' => 60, 'orderform_ad_weight' => 32],
            ['id' => 7, 'name' => '市級合夥人', 'ratio' => 2.75, 'contribution' => 132000, 'partner_bonus_ratio' => 80, 'orderform_ad_weight' => 64],
            ['id' => 8, 'name' => '省級合夥人', 'ratio' => 3, 'contribution' => 440000, 'partner_bonus_ratio' => 100, 'orderform_ad_weight' => 128],
        ]);
    }
}
