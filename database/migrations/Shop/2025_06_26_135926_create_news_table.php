<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('news', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('pic', 128)->nullable();
            $table->string('title', 36)->nullable();
            $table->text('description')->nullable()->comment('小說明');
            $table->text('content')->nullable();
            $table->dateTime('time')->useCurrent();
            $table->integer('online')->default(1);
            $table->integer('orders')->default(0)->comment('排序');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('news');
    }
};
