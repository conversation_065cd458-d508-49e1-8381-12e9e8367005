<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('center_level', function (Blueprint $table) {
            $table->float('cv_ratio', 8, 2)
                ->default(0)
                ->comment('CV金額分潤比率(使用時要除100)，小數點2位，最大值不可超過100');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('center_level', function (Blueprint $table) {
            $table->dropColumn('cv_ratio');
        });
    }
};
