<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('consumption_draw_record', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('user_id')->comment('會員id');
            $table->integer('pay_record_id')->comment('對應付款紀錄id(非0表掃碼付款) ');
            $table->integer('order_id')->comment('訂單id');
            $table->integer('draw_id')->comment('對應刮刮樂贈品id');
            $table->text('gift_pic')->nullable()->comment('贈品圖片');
            $table->text('gift_name')->nullable()->comment('贈品名稱');
            $table->text('createdate')->comment('建立日期');
            $table->boolean('show')->default(false)->comment('刮刮樂狀態 0.未刮 1.刮完');
            $table->text('ex_date')->nullable()->comment('兌換日期(無則表示未兌換)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('consumption_draw_record');
    }
};
