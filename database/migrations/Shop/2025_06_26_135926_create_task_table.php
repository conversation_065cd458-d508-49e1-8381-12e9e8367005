<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('task', function (Blueprint $table) {
            $table->comment('任務牆(未來可能添加更 bonus_column2...，或 rule_column1...，並根據不同 type 可有不同效力)');
            $table->integer('id', true);
            $table->char('name', 64)->comment('任務名稱');
            $table->integer('type')->comment('活動類型');
            $table->char('time_s', 10)->default('')->comment('開始時間(空表示無限制)(YYYY-mm-dd)');
            $table->char('time_e', 10)->default('')->comment('結束時間(空表示無限制)(YYYY-mm-dd)');
            $table->string('msg', 128)->comment('贈送記錄文字(消費者查看)');
            $table->char('bonus_column1', 11)->comment('回饋欄位1(依據活動類型有不同意義)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('task');
    }
};
