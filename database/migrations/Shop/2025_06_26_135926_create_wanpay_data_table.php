<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wanpay_data', function (Blueprint $table) {
            $table->integer('id', true);
            $table->text('pay_way')->nullable()->comment('無金流付款方式');
            $table->text('wanpay_way')->nullable()->comment('旺沛金流付款方式');
            $table->text('shop_no')->nullable()->comment('特店代號');
            $table->text('wanpay_key')->nullable()->comment('特店金鑰');
            $table->text('wanpay_content')->nullable()->comment('金流填寫內容');
            $table->string('regist_time', 10)->nullable()->comment('申請旺沛時間，吾則是未申請');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wanpay_data');
    }
};
