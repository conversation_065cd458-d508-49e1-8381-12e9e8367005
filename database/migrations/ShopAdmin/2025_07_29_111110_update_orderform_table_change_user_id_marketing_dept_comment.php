<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->table('orderform', function (Blueprint $table) {
            //先更改 comment
            $table->integer('user_id_operation')->length(11)->default(0)->comment('行政廣告部門(對應account的id)')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->table('orderform', function (Blueprint $table) {
            $table->integer('user_id_operation')->length(11)->default(0)->comment("營運者(對應account的id)")->change();
        });
    }
};
