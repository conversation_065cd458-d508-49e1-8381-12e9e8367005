<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FieldsSetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=fields_set --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'distributor_id' => 0,
                'title' => '姓名',
                'type' => 'text',
                'required' => 1,
                'special' => 0,
                'limit' => '.+',
                'discription' => '<br />',
                'options' => '["選項內容1","選項內容2","選項內容3","選項內容4"]',
                'order_id' => -100,
                'online' => 1,
            ],
            [
                'id' => 2,
                'distributor_id' => 0,
                'title' => '手機',
                'type' => 'text',
                'required' => 1,
                'special' => 0,
                'limit' => '^09\\d{8}$',
                'discription' => '請輸入10碼手機號:09XXXXXXXX',
                'options' => '[]',
                'order_id' => -99,
                'online' => 1,
            ]
        ];
        DB::table('fields_set')->truncate();
        DB::table("fields_set")->insert($dataTables);
    }
}
