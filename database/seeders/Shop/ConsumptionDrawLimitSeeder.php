<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConsumptionDrawLimitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=consumption_draw_limit --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'distributor_id' => 0,
                'price' => 500,
            ]
        ];
        DB::table('consumption_draw_limit')->truncate();
        DB::table("consumption_draw_limit")->insert($dataTables);
    }
}
