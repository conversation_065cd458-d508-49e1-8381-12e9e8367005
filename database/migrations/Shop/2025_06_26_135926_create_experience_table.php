<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('experience', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('pic', 128)->nullable();
            $table->string('title', 32)->nullable();
            $table->text('content')->nullable();
            $table->timestamp('time')->useCurrent();
            $table->boolean('online')->default(true);
            $table->integer('orders')->default(0)->comment('排序');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('experience');
    }
};
