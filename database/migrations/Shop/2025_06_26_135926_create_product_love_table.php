<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_love', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id')->comment('使用者id');
            $table->integer('product_id')->comment('商品id');
            $table->timestamp('datetime')->useCurrent()->comment('時間');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_love');
    }
};
