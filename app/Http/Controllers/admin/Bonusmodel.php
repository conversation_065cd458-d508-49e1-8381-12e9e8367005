<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\pattern\BonusSettingHelper;

class Bonusmodel extends MainController
{
    public function index(Request $request)
    {
        return view('admin.bonusmodel.index', ['data' => $this->data]);
    }

    public function get_data(Request $request)
    {
        $get_detail = $request->all();
        return BonusSettingHelper::get_bonus_models($get_detail);
    }

    public function save_data(Request $request)
    {
        $post_detail = $request->post('detail');
        // dd($post_detail);
        try {
            $detail_id = BonusSettingHelper::save_bonus_model($post_detail);
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success([
            'id' => $detail_id,
            'msg' => Lang::get('操作成功'),
        ]);
    }

    public function create(Request $request)
    {
        return view('admin.bonusmodel.create', ['data' => $this->data]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'normal_recommend' => 'nullable|numeric|min:0|max:100',
            'normal_partner' => 'nullable|numeric|min:0|max:100',
            'normal_marketing_dept' => 'nullable|numeric|min:0|max:100',
            'normal_sales_dept' => 'nullable|numeric|min:0|max:100',
            'normal_executive_director' => 'nullable|numeric|min:0|max:100',
            'normal_center_director' => 'nullable|numeric|min:0|max:100',
            'normal_center_founder' => 'nullable|numeric|min:0|max:100',
            'normal_lecturer' => 'nullable|numeric|min:0|max:100',
            'normal_center' => 'nullable|numeric|min:0|max:100',
            'normal_dividend_month' => 'nullable|numeric|min:0|max:100',
            'normal_center_divided_to_raiser' => 'nullable|numeric|min:0|max:100',
            'use_partner_mode' => 'required|in:0,1',
            'partner_recommend' => 'nullable|numeric|min:0|max:100',
            'partner_partner' => 'nullable|numeric|min:0|max:100',
            'partner_marketing_dept' => 'nullable|numeric|min:0|max:100',
            'partner_sales_dept' => 'nullable|numeric|min:0|max:100',
            'partner_executive_director' => 'nullable|numeric|min:0|max:100',
            'partner_center_director' => 'nullable|numeric|min:0|max:100',
            'partner_center_founder' => 'nullable|numeric|min:0|max:100',
            'partner_lecturer' => 'nullable|numeric|min:0|max:100',
            'partner_center' => 'nullable|numeric|min:0|max:100',
            'partner_dividend_month' => 'nullable|numeric|min:0|max:100',
            'partner_center_divided_to_raiser' => 'nullable|numeric|min:0|max:100',
            'ad_bonus' => 'nullable|numeric|min:0|max:100',
        ]);

        $detail = [
            'id' => 0,
            'name' => $request->input('name'),
            'normal_recommend' => $request->input('normal_recommend', ''),
            'normal_partner' => $request->input('normal_partner', ''),
            'normal_marketing_dept' => $request->input('normal_marketing_dept', ''),
            'normal_sales_dept' => $request->input('normal_sales_dept', ''),
            'normal_executive_director' => $request->input('normal_executive_director', ''),
            'normal_center_director' => $request->input('normal_center_director', ''),
            'normal_center_founder' => $request->input('normal_center_founder', ''),
            'normal_lecturer' => $request->input('normal_lecturer', ''),
            'normal_center' => $request->input('normal_center', ''),
            'normal_dividend_month' => $request->input('normal_dividend_month', ''),
            'normal_center_divided_to_raiser' => $request->input('normal_center_divided_to_raiser', ''),
            'use_partner_mode' => $request->input('use_partner_mode', 0),
            'partner_recommend' => $request->input('partner_recommend', ''),
            'partner_partner' => $request->input('partner_partner', ''),
            'partner_marketing_dept' => $request->input('partner_marketing_dept', ''),
            'partner_sales_dept' => $request->input('partner_sales_dept', ''),
            'partner_executive_director' => $request->input('partner_executive_director', ''),
            'partner_center_director' => $request->input('partner_center_director', ''),
            'partner_center_founder' => $request->input('partner_center_founder', ''),
            'partner_lecturer' => $request->input('partner_lecturer', ''),
            'partner_center' => $request->input('partner_center', ''),
            'partner_dividend_month' => $request->input('partner_dividend_month', ''),
            'partner_center_divided_to_raiser' => $request->input('partner_center_divided_to_raiser', ''),
            'ad_bonus' => $request->input('ad_bonus', ''),
        ];

        try {
            BonusSettingHelper::save_bonus_model($detail);
            return redirect('admin/bonusmodel/index')->with('success', '新增成功');
        } catch (\Throwable $th) {
            return back()->withErrors(['error' => $th->getMessage()])->withInput();
        }
    }

    public function edit($id)
    {
        try {
            $model_data = BonusSettingHelper::get_bonus_models(['id' => $id]);
            if (empty($model_data['db_data'])) {
                return redirect('admin/bonusmodel/index')->withErrors(['error' => '找不到指定的模組']);
            }

            $this->data['model'] = (object)$model_data['db_data'][0];
            return view('admin.bonusmodel.edit', ['data' => $this->data]);
        } catch (\Throwable $th) {
            return redirect('admin/bonusmodel/index')->withErrors(['error' => $th->getMessage()]);
        }
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'normal_recommend' => 'nullable|numeric|min:0|max:100',
            'normal_partner' => 'nullable|numeric|min:0|max:100',
            'normal_marketing_dept' => 'nullable|numeric|min:0|max:100',
            'normal_sales_dept' => 'nullable|numeric|min:0|max:100',
            'normal_executive_director' => 'nullable|numeric|min:0|max:100',
            'normal_center_director' => 'nullable|numeric|min:0|max:100',
            'normal_center_founder' => 'nullable|numeric|min:0|max:100',
            'normal_lecturer' => 'nullable|numeric|min:0|max:100',
            'normal_center' => 'nullable|numeric|min:0|max:100',
            'normal_dividend_month' => 'nullable|numeric|min:0|max:100',
            'normal_center_divided_to_raiser' => 'nullable|numeric|min:0|max:100',
            'use_partner_mode' => 'required|in:0,1',
            'partner_recommend' => 'nullable|numeric|min:0|max:100',
            'partner_partner' => 'nullable|numeric|min:0|max:100',
            'partner_marketing_dept' => 'nullable|numeric|min:0|max:100',
            'partner_sales_dept' => 'nullable|numeric|min:0|max:100',
            'partner_executive_director' => 'nullable|numeric|min:0|max:100',
            'partner_center_director' => 'nullable|numeric|min:0|max:100',
            'partner_center_founder' => 'nullable|numeric|min:0|max:100',
            'partner_lecturer' => 'nullable|numeric|min:0|max:100',
            'partner_center' => 'nullable|numeric|min:0|max:100',
            'partner_dividend_month' => 'nullable|numeric|min:0|max:100',
            'partner_center_divided_to_raiser' => 'nullable|numeric|min:0|max:100',
            'ad_bonus' => 'nullable|numeric|min:0|max:100',
        ]);

        $detail = [
            'id' => $id,
            'name' => $request->input('name'),
            'normal_recommend' => $request->input('normal_recommend', ''),
            'normal_partner' => $request->input('normal_partner', ''),
            'normal_marketing_dept' => $request->input('normal_marketing_dept', ''),
            'normal_sales_dept' => $request->input('normal_sales_dept', ''),
            'normal_executive_director' => $request->input('normal_executive_director', ''),
            'normal_center_director' => $request->input('normal_center_director', ''),
            'normal_center_founder' => $request->input('normal_center_founder', ''),
            'normal_lecturer' => $request->input('normal_lecturer', ''),
            'normal_center' => $request->input('normal_center', ''),
            'normal_dividend_month' => $request->input('normal_dividend_month', ''),
            'normal_center_divided_to_raiser' => $request->input('normal_center_divided_to_raiser', ''),
            'use_partner_mode' => $request->input('use_partner_mode', 0),
            'partner_recommend' => $request->input('partner_recommend', ''),
            'partner_partner' => $request->input('partner_partner', ''),
            'partner_marketing_dept' => $request->input('partner_marketing_dept', ''),
            'partner_sales_dept' => $request->input('partner_sales_dept', ''),
            'partner_executive_director' => $request->input('partner_executive_director', ''),
            'partner_center_director' => $request->input('partner_center_director', ''),
            'partner_center_founder' => $request->input('partner_center_founder', ''),
            'partner_lecturer' => $request->input('partner_lecturer', ''),
            'partner_center' => $request->input('partner_center', ''),
            'partner_dividend_month' => $request->input('partner_dividend_month', ''),
            'partner_center_divided_to_raiser' => $request->input('partner_center_divided_to_raiser', ''),
            'ad_bonus' => $request->input('ad_bonus', ''),
        ];

        try {
            BonusSettingHelper::save_bonus_model($detail);
            return redirect('admin/bonusmodel/index')->with('success', '更新成功');
        } catch (\Throwable $th) {
            return back()->withErrors(['error' => $th->getMessage()])->withInput();
        }
    }

    public function delete_data(Request $request)
    {
        $id = $request->post('id');
        // dd($id);
        try {
            $delete_result = BonusSettingHelper::delete_bonus_model($id);
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success(Lang::get('操作成功'));
    }
}
