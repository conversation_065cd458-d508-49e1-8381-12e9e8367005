<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=admin --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'name' => 'photonic管理員',
                'account' => 'photonic',
                'password' => '48913f026dabd50a7dbe3b4621e84a8d',
                'originalPassword' => 'photo3599',
                'email' => NULL,
                'permission' => 'all',
                'purview' => NULL,
            ],
            [
                'id' => 2,
                'name' => '管理員',
                'account' => 'admin',
                'password' => '7eaf94282adff201ee387c1f62b0d31e',
                'originalPassword' => 'maggiesky3388',
                'email' => '<EMAIL>',
                'permission' => 'no',
                'purview' => '{"10":["89"],"4":["16"],"2":["8","28","58"],"5":["78","59","91","75","13","14","69","88","74","25","3"],"3":["6","7","9","68","67","72","65","73","60"],"8":["27","64"]}',
            ],
            [
                'id' => 11,
                'name' => 535,
                'account' => 555,
                'password' => '15de21c670ae7c3f6f3f1f37029303c9',
                'originalPassword' => 555,
                'email' => '<EMAIL>',
                'permission' => 'no',
                'purview' => '""',
            ],
            [
                'id' => 16,
                'name' => '廠商管理員',
                'account' => 'admin0618',
                'password' => 'dabc871cfff8b4c47e610999e7187ac7',
                'originalPassword' => 'admin0618',
                'email' => '<EMAIL>',
                'permission' => 'current',
                'purview' => '{"10":["89"],"4":["16"],"2":["8","28","58"],"5":["78","59","91","75","13","14","69","88","74","25","3"],"3":["6","7","9","68","67","72","65","73","60"],"8":["27","64"]}',
            ],
            [
                'id' => 13,
                'name' => '王鈞賢',
                'account' => 's22222',
                'password' => '2aa997b8fcedde0b8b1d430704d322db',
                'originalPassword' => 51688,
                'email' => '<EMAIL>',
                'permission' => 'no',
                'purview' => '{"10":["21","84","85","86","97","98","101","96","87","94","95"],"5":["23"],"3":["26"],"7":["77","90","83","99"],"8":["92","93"]}',
            ],
            [
                'id' => 15,
                'name' => '客服',
                'account' => 's33333',
                'password' => '334930f6237bf19668609cf3673fe3f5',
                'originalPassword' => 888999,
                'email' => NULL,
                'permission' => 'no',
                'purview' => '{"10":["97","98","101","96","87","94","95"],"2":["61","2"],"5":["23","4","81","66","80"],"3":["26"],"7":["99"],"8":["92","93","62"]}',
            ]
        ];
        DB::table('admin')->truncate();
        DB::table("admin")->insert($dataTables);
    }
}
