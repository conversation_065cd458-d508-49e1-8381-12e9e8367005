<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SystemEmailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=system_email --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'signup_complete' => NULL,
                'contact_complete' => NULL,
                'order_complete' => NULL,
                'forget_password' => NULL,
                'order_cancel' => NULL,
                'product_qa' => NULL,
                'act_remind' => NULL,
                'act_cancel' => NULL,
            ]
        ];
        DB::table('system_email')->truncate();
        DB::table("system_email")->insert($dataTables);
    }
}
