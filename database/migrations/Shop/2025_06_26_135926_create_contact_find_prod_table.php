<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contact_find_prod', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('user_id');
            $table->text('user_name')->comment('姓名');
            $table->text('user_phone')->comment('手機');
            $table->text('user_email')->comment('信箱');
            $table->text('ask')->nullable()->comment('詢問內容{name, unit, num, img, note}');
            $table->text('createdate')->nullable()->comment('建立時間');
            $table->boolean('status')->default(false)->comment('狀態 0.未處理 1.以處理');
            $table->text('resp')->comment('回覆內容');
            $table->text('respdate')->nullable()->comment('回覆時間');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contact_find_prod');
    }
};
