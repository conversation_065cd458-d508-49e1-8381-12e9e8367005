<?php

namespace Database\Seeders\Shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PointsAllowUseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=points_allow_use --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'distributor_id' => 0,
                'value' => '["5","6"]',
            ]
        ];
        DB::table('points_allow_use')->truncate();
        DB::table("points_allow_use")->insert($dataTables);
    }
}
