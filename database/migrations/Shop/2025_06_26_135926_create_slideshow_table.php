<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('slideshow', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('title', 32);
            $table->string('pic', 128)->nullable();
            $table->string('link', 128)->nullable();
            $table->tinyInteger('online')->default(1)->comment('狀態 0.隱藏 1.顯示');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('slideshow');
    }
};
