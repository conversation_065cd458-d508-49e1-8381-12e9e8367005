<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('temp_order_data', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id')->default(0)->comment('會員id');
            $table->text('order_data')->comment('訂單內容');
            $table->text('cart_data')->comment('購物車資料');
            $table->text('randomkey')->comment('隨機碼');
            $table->text('time')->comment('時間');
            $table->boolean('over')->default(false)->comment('狀態 0.未使用 1.已使用');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('temp_order_data');
    }
};
