<?php

namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\BonusHelper;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class MemberInstance
{
    private $user_id;
    private $DBFileConnecter;
    static private $tableName = 'account';
    static private $orderTableName = 'orderform';
    static public $account_column = 'phone'; /*帳號欄位 email、phone*/
    static public $account_column_order = 'transport_email'; /*訂單帳號欄位(用於訂單建立會員資料) transport_email、transport_location_phone*/
    public $arr_partner_levels = [];  /*合夥人等級(以id為key)*/

    public function __construct($user_id = 0)
    {
        $this->user_id = $user_id;
        $this->DBFileConnecter = DBFileConnecter::withTableName('account', 'main_db');
        $this->arr_partner_levels = MemberInstance::get_partner_levels([], true)['db_data'];
    }
    /*取得會員資料庫連線*/
    static public function main_db()
    {
        return DB::connection('main_db');
    }
    /*更換當前會員id*/
    public function change_user_id($user_id)
    {
        $this->user_id = $user_id;
    }

    /*取得會員資料*/
    public function get_user_data($addr_change = "combine", $cond = [])
    {
        // dd($cond);
        $userData = self::main_db()->table(self::$tableName . ' as a')
            ->select('a.*', 'vip.id as vip_id', 'vip.vip_name')
            ->leftjoin('vip_type as vip', 'a.vip_type', 'vip.id');
        if ($cond) { /*有傳入篩選條件*/
            $userData->where($cond); /*依篩選條件搜尋*/
        } else {
            $userData = $userData->where('a.id', $this->user_id); /*依物件user_id搜尋*/
        }

        $userData = $userData->first();
        $userData = CommonService::objectToArray($userData);

        if ($userData) { /*額外為顯示處理資料*/
            /*生日轉換為日期*/
            $userData['birthday'] = $userData['birthday'] ? date('Y-m-d', $userData['birthday']) : "";

            /*處理地址*/
            if ($addr_change == "combine") { /*合併顯示地址*/
                try {
                    if ($userData['home']) {
                        $addcods = explode('|||', $userData['home']);

                        $city = DB::connection(config('A_sub'))->table('city')->whereRaw("AutoNo = " . $addcods[0])->get();
                        $city = CommonService::objectToArray($city);
                        $town = DB::connection(config('A_sub'))->table('town')->whereRaw("AutoNo = " . $addcods[1])->get();
                        $town = CommonService::objectToArray($town);
                        $post = $addcods[2];
                        $otheradd = $addcods[3];

                        $userData['home'] = $post . ' ' . $city[0]['Name'] . $town[0]['Name'] . $otheradd;
                    }
                } catch (\Exception $e) {
                    $addcods = explode('|||', $userData['home']);
                    $userData['home'] = count($addcods) > 1 ? ' ' : $userData['home'];
                }
            } else if ($addr_change == "split") { /*獨立成縣市、區、郵遞區號*/
                $userData['F_I_CNo'] = '';
                $userData['F_I_TNo'] = '';
                $userData['F_S_NH_Zip'] = '';
                $userData['F_S_NH_Address'] = '';
                $userData['F_I_CNo_Name'] = '';
                $userData['F_I_TNo_Name'] = '';

                if ($TryStrpos = strpos($userData['home'], "|||")) {
                    $ex = explode("|||", $userData['home']);
                    $userData['F_I_CNo'] = $ex[0];
                    $userData['F_I_TNo'] = $ex[1];
                    $userData['F_S_NH_Zip'] = $ex[2];
                    $userData['F_S_NH_Address'] = $ex[3];

                    if (!empty($userData['F_I_CNo']))
                        $userData['F_I_CNo_Name'] = DB::table('city')->whereRaw(" AutoNo = '" . $userData['F_I_CNo'] . "'")->select('Name')->first()->Name;

                    if (!empty($userData['F_I_TNo']))
                        $userData['F_I_TNo_Name'] = DB::table('town')->whereRaw(" AutoNo = '" . $userData['F_I_TNo'] . "'")->select('Name')->first()->Name;
                } else {
                    $userData['F_S_NH_Address'] = $userData['home'];
                }
            }
        }
        return $userData;
    }
    /*取得會員資料(判斷是否為供應商)*/
    public function get_user_data_distributor($addr_change = "combine", $cond = [])
    {
        $userData = $this->get_user_data($addr_change, $cond);
        // dd($userData);
        if ($userData) {
            if ($userData['user_type'] == 1) {
                if (!$userData['shop_name']) {
                    $userData['shop_name'] = $userData['name'];
                }
                return $userData;
            }
        }
        return null;
    }
    /*修改會員資料*/
    public function update_user_data($updateData, $cond = [], $change_format = true)
    {
        $returnData = ['code' => 0, 'msg' => "", 'data' => []];
        if (empty($updateData)) {
            $returnData['msg'] = Lang::get('資料不完整');
        }
        unset($updateData['_token']);
        if (isset($updateData[self::$account_column])) { /*要修改帳號*/
            /*檢查是否有與「其他」帳號重複*/
            $adminData = $this->get_user_data($addr_change = "ori", [
                ['a.' . self::$account_column, '=', $updateData[self::$account_column]],
                ['a.id', '<>', $this->user_id]
            ]);
            if ($adminData) {
                $returnData['msg'] = Lang::get('帳號已經存在');
                return $returnData;
            }
        }

        if ($change_format) { /*調整帳號資料*/
            $returnData = self::arrange_data_to_db_format($updateData);
            if ($returnData['code'] == 0) { /*調整資料時有錯誤訊息*/
                return $returnData;
            } else {
                $updateData = $returnData['data'];
            }
        }
        unset($updateData['id']);

        if (isset($updateData['upline_user'])) {
            if ($updateData['upline_user'] != 0) {
                $has_user = self::main_db()->table('account')->where('id', $updateData['upline_user'])->first();
                if (!$has_user) {
                    $returnData['code'] = 0;
                    $returnData['msg'] = Lang::get('推薦者有誤，會員不存在');
                    return $returnData;
                }
            }
        }
        if (isset($updateData['center_raiser_id'])) {
            if ($updateData['center_raiser_id'] != 0) {
                $has_user = self::main_db()->table('account')->where('id', $updateData['center_raiser_id'])->first();
                if (!$has_user) {
                    $returnData['code'] = 0;
                    $returnData['msg'] = Lang::get('發起者有誤，會員不存在');
                    return $returnData;
                }
            }
        }

        /*篩選要修改的對象*/
        $userData = self::main_db()->table(self::$tableName . ' as a');
        if ($cond) { /*有傳入篩選條件*/
            $userData = $userData->where($cond); /*依篩選條件搜尋*/
        } else {
            $userData = $userData->where('a.id', $this->user_id); /*依物件user_id搜尋*/
        }
        $userData2 = clone $userData;
        $target_data = $userData2->first();
        $target_data = CommonService::objectToArray($target_data);

        /*檢查是否累積自動升級中*/
        if (isset($updateData['auto_partner'])) {
            if (
                $updateData['auto_partner'] == 1 &&
                ($target_data['increasing_limit_invest'] ?? 0) < 0
            ) {
                $returnData['code'] = 0;
                $returnData['msg'] = Lang::get('累積自動升級中，無法處理');
                return $returnData;
            }
        }

        /*處理檔案上傳*/
        if ($target_data) {
            $user_id = $target_data['id'];
            $file_company = request()->file('file_company');
            // dump($file_company);
            if ($file_company) {
                $file_company = $this->DBFileConnecter->fixedFileUp($file_company, 'user_file_company' . CommonService::geraHash(10) . '_' . $user_id);
                if ($file_company) {
                    $updateData['file_company'] = $file_company;
                }
            }
            $file_person = request()->file('file_person');
            // dump($file_person);
            if ($file_person) {
                $file_person = $this->DBFileConnecter->fixedFileUp($file_person, 'user_file_person' . CommonService::geraHash(10) . '_' . $user_id);
                if ($file_person) {
                    $updateData['file_person'] = $file_person;
                }
            }
        }

        /*修改資料*/
        // dd($updateData);
        if (isset($updateData['_token'])) {
            $remove['_token'] = $updateData['_token'];
            $updateData = array_diff_key($updateData, $remove);
        }
        if (!empty($updateData)) {
            $result = $userData->update($updateData);

            if ($result) {
                $returnData['code'] = 1;
                $returnData['msg'] = Lang::get('操作成功');
                $returnData['data'] = $this->get_user_data($addr_change = "combine", $cond);
            } else {
                $returnData['msg'] = Lang::get('無資料需要修改');
            }
        } else {
            $returnData['msg'] = Lang::get('無資料需要修改');
        }

        return $returnData;
    }
    /*新增會員資料*/
    public function insert_user_data($newData, $change_format = true)
    {
        $returnData = ['code' => 0, 'msg' => "", 'data' => []];

        if ($change_format) { /*調整帳號資料*/
            $db_formatData = self::arrange_data_to_db_format($newData);
            if ($db_formatData['code'] == 0) { /*調整資料時有錯誤訊息*/
                return $db_formatData;
            } else {
                $newData = $db_formatData['data'];
            }
        }

        /* 判斷帳號唯一性 */
        $where = [];
        if (isset($newData['id'])) { /*有設定id*/
            if ($newData['id'] != "") {
                $where[] = ['a.id', '=', $newData['id']];
            }
        }
        if (isset($newData[self::$account_column])) { /*有設定帳號*/
            if ($newData[self::$account_column] != "") {
                $where[] = ['a.' . self::$account_column, '=', $newData[self::$account_column]];
            }
        }

        if (isset($newData['gmail'])) { /*有設定goolge帳號*/
            if ($newData['gmail'] != "") {
                $where[] = ['a.gmail', '=', $newData['gmail']];
            }
        }
        if (isset($newData['line_id'])) { /*有設定line_id*/
            if ($newData['line_id'] != "") {
                $where[] = ['a.line_id', '=', $newData['line_id']];
            }
        }
        if (isset($newData['FB_id'])) { /*有設定FB_id*/
            if ($newData['FB_id'] != "") {
                $where[] = ['a.FB_id', '=', $newData['FB_id']];
            }
        }
        if ($where) {
            $accounts = $this->get_user_data($addr_change = "ori", $where);

            if ($accounts) {
                $returnData['msg'] = Lang::get('帳號已經存在');
                return $returnData;
            }
        }

        /*額外新會員資料*/
        if (!isset($newData['number'])) {
            $newData['number'] = config('extra.shop.subDeparment') . 'US' . date('Ymd') . self::getMemberNumber();
        }
        $newData['createtime'] = time();

        $newData['upline_user'] = isset($newData['upline_user']) ? $newData['upline_user'] : 0;
        if ($newData['upline_user']) {
            $recommend_user = self::main_db()->table('account')->select('id', 'name', 'number', 'recommend_content')->where('number', $newData['upline_user'])->first();
            $recommend_user = CommonService::objectToArray($recommend_user);
            $newData['upline_user'] = $recommend_user ? $recommend_user['id'] : 0;
        } else {
            $newData['upline_user'] = 0;
        }

        // dd($newData);
        self::main_db()->table(self::$tableName)->insert($newData);
        $target_data = $this->get_user_data($addr_change = "combine", $where);

        /*處理檔案*/
        $this->update_user_data([], $where);

        $returnData['data'] = $target_data;
        $returnData['code'] = 1;
        return $returnData;
    }

    /*批次新增會員(excel匯入&牛魔王串接)*/
    public function import_member(array $array_member_data)
    {
        $user_collection = self::main_db()->table(self::$tableName)->get();
        $db_number_to_user = $user_collection->keyBy('number')->toArray();
        $db_phone_to_user =  $user_collection->keyBy('phone')->toArray();
        // dump($db_number_to_user);dump($db_phone_to_user);exit;
        $repeat_number = [];
        $repeat_phone = [];
        $number_to_upline_user = [];
        $number_to_center_raiser_id = [];

        $arr_registration_from = [];
        foreach (MemberInstance::get_registration_from([], true)['db_data'] as $key => $value) {
            $arr_registration_from[$value['name']] = $value;
        }
        $arr_vip_types = [];
        foreach (MemberInstance::get_vip_types([], true)['db_data'] as $key => $value) {
            $arr_vip_types[$value['vip_name']] = $value;
        }
        $arr_partner_levels = [];
        foreach (MemberInstance::get_partner_levels([], true)['db_data'] as $key => $value) {
            $arr_partner_levels[$value['name']] = $value;
        }
        $arr_center_levels = [];
        foreach (MemberInstance::get_center_levels([], true)['db_data'] as $key => $value) {
            $arr_center_levels[$value['name']] = $value;
        }

        $save_data = [];
        $array_error_data = [];
        foreach ($array_member_data as $key => $data) {
            if (!$data['number']) {
                array_push($array_error_data, ['data' => $data, 'error_msg' => '未設定會員編號']);
                continue;
            }
            if (!$data['phone']) {
                array_push($array_error_data, ['data' => $data, 'error_msg' => '未設定會員手機']);
                continue;
            }
            $data['pwd'] = 'a123456';

            $returnData = self::arrange_data_to_db_format($data);
            if ($returnData['code'] == 0) {
                array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => $returnData['msg']]);
                continue;
            } else {
                if (isset($db_number_to_user[$returnData['data']['number']]) || isset($repeat_number[$returnData['data']['number']])) {
                    array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '會員編號已存在']);
                    continue;
                } else if (isset($db_phone_to_user[$returnData['data']['phone']]) || isset($repeat_phone[$returnData['data']['phone']])) {
                    array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '會員手機已存在']);
                    continue;
                } else {
                    $repeat_number[$returnData['data']['number']] = 1;
                    $repeat_phone[$returnData['data']['phone']] = 1;
                }
            }

            /*天脈對應欄位調整*/
            /*推荐人設定*/
            if ($returnData['data']['upline_user'] ?? '') {
                if (isset($db_number_to_user[$returnData['data']['upline_user']])) { /*存在於資料庫*/
                    /*略過調整新增的資料，因為「insert_user_data」會轉換「會員編號」成「會員id」*/
                } else if (isset($repeat_number[$returnData['data']['upline_user']])) { /*存在於檔案*/
                    if (!isset($number_to_upline_user[$returnData['data']['upline_user']])) { /*紀錄新增後需要編輯的對象*/
                        $number_to_upline_user[$returnData['data']['upline_user']] = [
                            'id' => 0, /*預設0，被新增後將設定為系統ID*/
                            'users' => [], /*記錄需修改推荐人的會員帳號*/
                        ];
                    }
                    array_push($number_to_upline_user[$returnData['data']['upline_user']]['users'], $returnData['data'][MemberInstance::$account_column]);
                    $returnData['data']['upline_user'] = 0;
                } else {
                    array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '推荐人不存在']);
                    continue;
                }
            }
            /*發起人*/
            if ($returnData['data']['center_raiser_id'] ?? '') {
                if (isset($db_number_to_user[$returnData['data']['center_raiser_id']])) { /*存在於資料庫*/
                    $returnData['data']['center_raiser_id'] = $db_number_to_user[$returnData['data']['center_raiser_id']]->id; /*調整新增的資料*/
                } else if (isset($repeat_number[$returnData['data']['center_raiser_id']])) { /*紀錄新增後需要編輯的對象*/
                    if (!isset($number_to_center_raiser_id[$returnData['data']['center_raiser_id']])) {
                        $number_to_center_raiser_id[$returnData['data']['center_raiser_id']] = [
                            'id' => 0, /*預設0，被新增後將設定為系統ID*/
                            'users' => [], /*記錄需修改中心發起人的會員帳號*/
                        ];
                    }
                    array_push($number_to_center_raiser_id[$returnData['data']['center_raiser_id']]['users'], $returnData['data'][MemberInstance::$account_column]);
                    $returnData['data']['center_raiser_id'] = 0;
                } else {
                    array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '發起人不存在']);
                    continue;
                }
            }
            /*會員來源*/
            if ($returnData['data']['registration_from'] ?? '') {
                if (isset($arr_registration_from[$returnData['data']['registration_from']])) {
                    $returnData['data']['registration_from'] = $arr_registration_from[$returnData['data']['registration_from']]['id'];
                } else {
                    array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '會員來源設定有誤']);
                    continue;
                }
            } else {
                array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '會員來源未設定']);
                continue;
            }
            /*會員級別*/
            if ($returnData['data']['vip_type'] ?? '') {
                if (isset($arr_vip_types[$returnData['data']['vip_type']])) {
                    $returnData['data']['vip_type'] = $arr_vip_types[$returnData['data']['vip_type']]['id'];
                } else {
                    array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '會員級別設定有誤']);
                    continue;
                }
            }
            /*課程進度*/
            if ($returnData['data']['vip_type_course'] ?? '') {
                if (isset($arr_vip_types[$returnData['data']['vip_type_course']]['id'])) {
                    $returnData['data']['vip_type_course'] = $arr_vip_types[$returnData['data']['vip_type_course']]['id'];
                } else {
                    array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '課程進度設定有誤']);
                    continue;
                }
            }
            /*中心級別*/
            if ($returnData['data']['center_level_id'] ?? '') {
                if (isset($arr_center_levels[$returnData['data']['center_level_id']])) {
                    $returnData['data']['center_level_id'] = $arr_center_levels[$returnData['data']['center_level_id']]['id'];
                } else {
                    array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '中心級別設定有誤']);
                    continue;
                }
            }
            /*合夥人等級*/
            if ($returnData['data']['partner_level_id'] ?? '') {
                if (isset($arr_partner_levels[$returnData['data']['partner_level_id']])) {
                    $returnData['data']['partner_level_id'] = $arr_partner_levels[$returnData['data']['partner_level_id']]['id'];
                } else {
                    array_push($array_error_data, ['data' => $returnData['data'], 'error_msg' => '合夥人等級設定有誤']);
                    continue;
                }
            }

            // 紀錄整理後的會員資料
            array_push($save_data, $returnData['data']);
        }
        /*有錯誤資料*/
        if ($array_error_data) {
            return $array_error_data;
        }

        // dump($number_to_upline_user);
        // dump($number_to_center_raiser_id);
        // dump($save_data);exit;
        foreach ($save_data as $key => $value) {
            $account = $this->get_user_data($addr_change = "ori", ['a.email' => array('eq', $value['email'])]);
            if ($account) { /*帳號已存在，視為編輯*/
                unset($value['email']);
                $this->update_user_data($value, $cond = [['a.id', '=', $account['id']]], $change_format = false);
            } else { /*帳號不存在，視為新增*/
                $value['status'] = 1;
                $returnData = $this->insert_user_data($value, $change_format = false);
                if (isset($number_to_upline_user[$value['number']])) {
                    $number_to_upline_user[$value['number']]['id'] = $returnData['data']['id'];
                }
                if (isset($number_to_center_raiser_id[$value['number']])) {
                    $number_to_center_raiser_id[$value['number']]['id'] = $returnData['data']['id'];
                }
            }
        }
        /*建立完會員後修改推薦者*/
        foreach ($number_to_upline_user as $member_number => $item) {
            self::main_db()->table(self::$tableName)->whereIn(self::$account_column, $item['users'])->update(['upline_user' => $item['id']]);
        }
        /*建立完會員後修改發起者*/
        foreach ($number_to_center_raiser_id as $member_number => $item) {
            self::main_db()->table(self::$tableName)->whereIn(self::$account_column, $item['users'])->update(['center_raiser_id' => $item['id']]);
        }

        return [];
    }

    /*依條件計算當前會員的訂單資料*/
    public function get_user_order_data($cond = [])
    {
        $orderform = self::main_db()->table(self::$orderTableName)->where('user_id', $this->user_id);

        if (isset($cond['status'])) { /*篩選訂單狀態*/
            if ($cond['status'] == 'All') { /*全部的成立訂單*/
                $orderform = $orderform->whereRaw('status not in ("Cancel", "Return")');
            } else if ($cond['status'] == 'All_no') { /*全部的失效訂單*/
                $orderform = $orderform->whereRaw('status in ("Cancel", "Return")');
            } else {
                $orderform = $orderform->where('status', $cond['status']);
            }
        }

        /*判斷計算方式*/
        $method = isset($cond['method']) ? $cond['method'] : 'sum'; /*預設加總金額*/
        if ($method == 'select') {
            $calculated_order_price = $orderform->orderByRaw('id desc')->get();
            $calculated_order_price = CommonService::objectToArray($calculated_order_price);
        } else if ($method == 'count') {
            $calculated_order_price = $orderform->count();
        } else {
            $calculated_order_price = $orderform->sum('total');
        }

        return $calculated_order_price;
    }

    /*替當前會員建立抽抽樂紀錄*/
    public function set_lucky_draw($price = 0, $pay_record_id = 0, $order_id = 0)
    {
        $times = 0;

        /*檢查登入(使用者資料)*/
        $user_data = $this->get_user_data();
        if (!$user_data) {
            return $times;
        }

        /*檢查會員等級*/
        $vip_type_id = $user_data['vip_type'];
        $vip_type_id = $vip_type_id == 0 && $vip_type_id == "" ? "-1" : $vip_type_id;
        $vip_type = self::main_db()->table('vip_type')->find($vip_type_id);
        $vip_type = CommonService::objectToArray($vip_type);
        if (!$vip_type) { /*沒設定為VIP會員(不限等級) 或 設定之等級不存在*/
            return $times;
        } else if ($vip_type['id'] == '0') {
            return $times;
        }

        /*計算消費金額可刮次數*/
        $limit_price = DB::table('consumption_draw_limit')->find(1)->price;
        $times = floor($price / $limit_price);
        if ($times < 1) return $times;

        /*逐次建立刮刮樂資料*/
        foreach (range(1, $times) as $once) {
            /*從1到所有啟用獎品之數量加總中 隨機取得一亂數*/
            $ratios = DB::table('consumption_draw')->where('online', 1)->sum('ratio');
            $rand_num = rand(1, $ratios);

            /*根據取得的亂數確認中獎項目*/
            $draw_id = 0;
            $gift_pic = "";
            $gift_name = "";
            $calculated_num = 0;
            $consumption_draws = DB::table('consumption_draw')->where('online', 1)->orderByRaw('ratio asc, id desc')->get();
            $consumption_draws = CommonService::objectToArray($consumption_draws);
            foreach ($consumption_draws as $consumption_draw) {
                $calculated_num += $consumption_draw['ratio'];
                if ($calculated_num >= $rand_num) {
                    $draw_id = $consumption_draw["id"];
                    $gift_pic = $consumption_draw["pic"];
                    $gift_name = $consumption_draw["name"];
                    break;
                }
            }

            $data = [
                'user_id' => $this->user_id,
                'pay_record_id' => $pay_record_id,
                'order_id' => $order_id,
                'draw_id' => $draw_id,
                'gift_pic' => $gift_pic,
                'gift_name' => $gift_name,
                'createdate' => time(),
                'show' => 0,
            ];
            DB::table('consumption_draw_record')->insert($data);
        }

        return $times;
    }

    public function get_increasing_limit_records($params, $join_account = false)
    {
        $db = self::main_db()->table('increasing_limit_record AS ilr');

        /*篩選欄位*/
        $field_query = 'ilr.*, FROM_UNIXTIME(create_time) AS create_time_f';
        if ($join_account) {
            $field_query .= ', a.name, a.number, a.user_type';
        }
        $db = $db->selectRaw($field_query);
        if ($join_account) {
            $db = $db->join('account AS a', 'a.id', 'ilr.user_id', 'LEFT');
        }

        /*篩選條件*/
        if (isset($params['user_id'])) {
            $db = $db->where('ilr.user_id', '=', $params['user_id']);
        }
        if (isset($params['limit_type'])) {
            $db = $db->where('ilr.limit_type', '=', $params['limit_type']);
        }
        if (isset($params['date_s'])) {
            if ($params['date_s']) {
                $db = $db->where('ilr.create_time', '>=', strtotime($params['date_s']));
            }
        }
        if (isset($params['date_e'])) {
            if ($params['date_e']) {
                $db = $db->where('ilr.create_time', '<', strtotime($params['date_e'] . '+1Day'));
            }
        }
        if (isset($params['point_msg'])) {
            $db = $db->where('ilr.msg', 'LIKE', '%' . $params['point_msg'] . '%');
        }
        if ($join_account) {
            if (isset($params['user_key'])) {
                $db = $db->where(function ($query) use ($params) {
                    $query->where('a.name', 'LIKE', '%' . $params['user_key'] . '%')
                        ->orWhere('a.number', 'LIKE', '%' . $params['user_key'] . '%');
                });
            }
        }

        /*分頁*/
        if (isset($params['count_of_items'])) { /*有傳入一頁數量*/
            $index_and_length = CommonService::get_model_limit_index_and_length($params['count_of_items'], $params['page'] ?? 1);
            if ($index_and_length[1]) { /*需依分頁篩選*/
                $db = $db->offset($index_and_length[0])->limit($index_and_length[1]);
            }
        }

        $records = $db->orderBy('ilr.id', 'desc')->get();
        $records = CommonService::objectToArray($records);
        return $records;
    }
    /*添加「功德圓滿點數」紀錄&調整會員功德圓滿點數*/
    public function add_increasing_limit_record(float $change_num, string $msg, int $limit_type, int $type = 1, string $create_time = null)
    {
        $limit_column = '';
        if ($limit_type == 1) {
            $limit_column = 'increasing_limit_invest';
        } else if ($limit_type == 2) {
            $limit_column = 'increasing_limit_consumption';
        } else if ($limit_type == 3) {
            $limit_column = 'increasing_limit_other';
        }
        if (!$limit_column) {
            return [];
        }
        if ($change_num == 0) {
            return [];
        }

        if (!$create_time) {
            $create_time = time();
        }

        $insertData = [
            'user_id' => $this->user_id,
            'num' => $change_num,
            'msg' => $msg,
            'limit_type' => $limit_type,
            'type' => $type,
            'create_time' => $create_time,
        ];
        $records = self::main_db()->table('increasing_limit_record')->insertGetId($insertData);
        self::main_db()->table('account')->where('id', $this->user_id)->increment($limit_column, $insertData['num']);
        return $records;
    }
    /*依實領金額(美金)添加提現紀錄 */
    public function add_point_to_cash_record(string $currency, float $cash_point)
    {
        if ($cash_point <= 0) {
            return [];
        }

        $show_dollar_set_keys = array_keys(config('extra.skychakra.show_dollar_set'));
        if (!in_array($currency, $show_dollar_set_keys)) {
            throw new \Exception(Lang::get('無此幣別'));
        }
        $exchange_reate = config('extra.skychakra.exchange_rate_set')[$currency] ?? 1;
        $show_dollar = config('extra.skychakra.show_dollar_set')[$currency] ?? 'USD$';
        /*依匯率計算點數對應金額(取整數)*/
        $cash_num = floor($cash_point * $exchange_reate);
        $insertData = [
            'user_id' => $this->user_id,
            'currency' => $show_dollar,
            'num' => $cash_num,
            'time_create' => time(),
        ];
        // dump($insertData);exit;
        $records = self::main_db()->table('points_to_cash_record')->insertGetId($insertData);
        return $records;
    }
    public function get_cash_record_data($params, $join_account = false)
    {
        $db = self::main_db()->table('points_to_cash_record AS pcr');

        /*篩選欄位*/
        $field_query = 'pcr.*, FROM_UNIXTIME(pcr.time_create) AS time_create_f, FROM_UNIXTIME(pcr.time_pay) AS time_pay_f';
        if ($join_account) {
            $field_query .= ', a.name, a.number, a.user_type';
        }
        $db = $db->selectRaw($field_query);
        if ($join_account) {
            $db = $db->join('account AS a', 'a.id', 'pcr.user_id', 'LEFT');
        }

        /*篩選條件*/
        if (isset($params['id'])) {
            $db = $db->where('pcr.id', '=', $params['id']);
        }
        if (isset($params['user_id'])) {
            $db = $db->where('pcr.user_id', '=', $params['user_id']);
        }
        if (isset($params['paid'])) {
            if ($params['paid'] == 1) { /*未給付*/
                $db = $db->where('time_pay', '=', '');
            } else if ($params['paid'] == 2) { /*完成給付*/
                $db = $db->where('time_pay', '!=', '');
            }
        }
        if (isset($params['date_s'])) {
            if ($params['date_s']) {
                $db = $db->where('pcr.time_create', '>=', strtotime($params['date_s']));
            }
        }
        if (isset($params['date_e'])) {
            if ($params['date_e']) {
                $db = $db->where('pcr.time_create', '<', strtotime($params['date_e'] . '+1Day'));
            }
        }
        if (isset($params['date_s_pay'])) {
            if ($params['date_s_pay']) {
                $db = $db->where('pcr.time_pay', '>=', strtotime($params['date_s_pay']));
            }
        }
        if (isset($params['date_e_pay'])) {
            if ($params['date_e_pay']) {
                $db = $db->where('pcr.time_pay', '<', strtotime($params['date_e_pay'] . '+1Day'))
                    ->where('time_pay', '!=', '');
            }
        }

        if ($join_account) {
            if (isset($params['memberKey'])) {
                $db = $db->where(function ($query) use ($params) {
                    $query->where('a.number', 'LIKE', '%' . $params['memberKey'] . '%')
                        ->orWhere('a.name', 'LIKE', '%' . $params['memberKey'] . '%')
                        ->orWhere('a.email', 'LIKE', '%' . $params['memberKey'] . '%')
                        ->orWhere('a.phone', 'LIKE', '%' . $params['memberKey'] . '%');
                });
            }
        }

        /*分頁*/
        if (isset($params['count_of_items'])) { /*有傳入一頁數量*/
            $index_and_length = CommonService::get_model_limit_index_and_length($params['count_of_items'], $params['page'] ?? 1);
            if ($index_and_length[1]) { /*需依分頁篩選*/
                $db = $db->offset($index_and_length[0])->limit($index_and_length[1]);
            }
        }

        $records = $db->orderBy('pcr.id', 'desc')->get();
        $records = CommonService::objectToArray($records);
        return $records;
    }


    /*依搜尋條件取得會員列表資料*/
    static public function search_member($status, $request, $field = 'a.*,vip_type.id as vip_id,vip_type.vip_name')
    {
        // 取得搜尋變數
        $tag = $request->get('tag') ?? '1';
        $search_result['tag'] = $tag;

        $account_column_value = $request->get(self::$account_column) ?? '';
        $search_result[self::$account_column] = $account_column_value;

        /*會員搜尋資料*/
        $memberKey = $request->get('memberKey') ?? '';
        $memberKey = trim($memberKey);
        $search_result['memberKey'] = $memberKey;

        $nameKey = $request->get('nameKey') ?? '';
        $search_result['nameKey'] = $nameKey;

        $vipType = $request->get('vipType') ?? '';
        $search_result['vipType'] = $vipType;

        $partner_level_id = $request->get('partner_level_id') ?? '';
        $search_result['partner_level_id'] = $partner_level_id;

        $center_level_id = $request->get('center_level_id') ?? '';
        $search_result['center_level_id'] = $center_level_id;

        $userType = $request->get('userType') ?? '';
        $search_result['userType'] = $userType;

        $date_st = $request->get('date_st') ?? '';
        $search_result['date_st'] = $date_st;

        $date_en = $request->get('date_en') ?? '';
        $search_result['date_en'] = $date_en;

        $user_type = $request->get('user_type') ?? '';
        $search_result['user_type'] = $user_type;

        $upline_user = $request->get('upline_user') ?? '';
        $search_result['upline_user'] = $upline_user;

        /*註冊商品搜尋資料*/
        $searchKey1 = $request->get('searchKey1') ?? '';
        $searchKey1 = trim($searchKey1);
        $search_result['searchKey1'] = $searchKey1;

        $buy_date_st1 = $request->get('buy_date_st1') ?? '';
        $search_result['buy_date_st1'] = $buy_date_st1;

        $buy_date_en1 = $request->get('buy_date_en1') ?? '';
        $search_result['buy_date_en1'] = $buy_date_en1;

        $reg_date_st = $request->get('reg_date_st') ?? '';
        $search_result['reg_date_st'] = $reg_date_st;

        $reg_date_en = $request->get('reg_date_en') ?? '';
        $search_result['reg_date_en'] = $reg_date_en;

        /*購買商品搜尋資料*/
        $searchKey2 = $request->get('searchKey2') ?? '';
        $searchKey2 = trim($searchKey2);
        $search_result['searchKey2'] = $searchKey2;

        $buy_date_st2 = $request->get('buy_date_st2') ?? '';
        $search_result['buy_date_st2'] = $buy_date_st2;

        $buy_date_en2 = $request->get('buy_date_en2') ?? '';
        $search_result['buy_date_en2'] = $buy_date_en2;

        // 處理sql篩選語句
        $account_number_list = '';
        $number_list = 'true ';
        $excel_wh = '';
        $account_number = '';
        $his_number = '';
        $excel_number = 0;
        $order_number = 0;

        if ($account_column_value) { /*用於比對帳號*/
            $number_list .= "and a." . self::$account_column . "='" . $account_column_value . "'";
        }

        $number_list .= "and ( a.name like '%" . $memberKey . "%' or
                      a.number like '%" . $memberKey . "%' or
                      a.email like '%" . $memberKey . "%' or
                      a.phone like '%" . $memberKey . "%' )";

        if ($date_st != '') {
            $number_list .= "and (a.createtime BETWEEN '" . strtotime($date_st) . "' and '" . (strtotime($date_en) + 84600) . "')";
        }

        if ($vipType != '') {
            if ($vipType == "-1") { // 搜尋無等級會員
                $number_list .= " and a.vip_type=0 ";
            } else { // 搜尋其他等級會員
                $number_list .= " and a.vip_type=" . $vipType;
            }
        }

        if ($partner_level_id != '') {
            if ($partner_level_id == "-1") { // 搜尋無合夥等級
                $number_list .= " and a.partner_level_id=0 ";
            } else { // 搜尋其他等級會員
                $number_list .= " and a.partner_level_id=" . $partner_level_id;
            }
        }
        if ($center_level_id != '') {
            if ($center_level_id == "-1") { // 搜尋無中心等級
                $number_list .= " and a.center_level_id=0 ";
            } else { // 搜尋其他等級會員
                $number_list .= " and a.center_level_id=" . $center_level_id;
            }
        }

        if ($userType != '') {
            $number_list .= " and a.user_type=" . $userType;
        }
        if ($user_type != '') {
            $number_list .= " and a.user_type=" . $user_type;
        }
        if ($upline_user != '') {
            $number_list .= " and a.upline_user=" . $upline_user;
        }
        if ($status !== '') {
            $number_list .= " and a.status='" . $status . "'";
        }
        //dd($number_list);
        $field = array_map('trim', explode(',', $field));
        // dump($field);exit;
        $rowData = self::main_db()->table(self::$tableName . ' as a')
            ->select($field)
            ->leftJoin('vip_type', 'a.vip_type', '=', 'vip_type.id')
            ->whereRaw($number_list)
            ->orderByRaw('a.id desc')
            ->get();
        $rowData = CommonService::objectToArray($rowData);

        $obj_partner_level = self::get_partner_levels([], true)['db_data'];
        $obj_center_level = self::get_center_levels([], true)['db_data'];
        foreach ($rowData as $key => $value) {
            if (isset($value['partner_level_id'])) {
                $rowData[$key]['partner_level_name'] = $obj_partner_level[$value['partner_level_id']]['name'] ?? '無';
            }
            if (isset($value['center_level_id'])) {
                $rowData[$key]['center_level_name'] = $obj_center_level[$value['center_level_id']]['name'] ?? '無';
            }
        }
        $search_result['rowData'] = $rowData;

        $do_number = 0;
        $h_number = []; // 已註冊/已購買的會員
        $n_number = []; // 未註冊/未購買的會員
        if ($nameKey == "1") { // 註冊商品搜尋
            foreach ($rowData as $key => $value) {
                $reg_where = "";

                $reg_where = "account_number = '" . $value['id'] . "'";
                if ($searchKey1 != '') {
                    if ($reg_where != '')
                        $reg_where .= " and ";

                    $reg_where .= "product_name like '%" . $searchKey1 . "%'";
                }


                if ($buy_date_st1 != '') {
                    if ($reg_where != '')
                        $reg_where .= " and ";
                    $reg_where .= "(buytime BETWEEN '" . $buy_date_st1 . "' and '" . $buy_date_en1 . "' ) ";
                }

                if ($reg_date_st != '') {
                    if ($reg_where != '')
                        $reg_where .= " and ";
                    $reg_where .= "(regtime BETWEEN '" . $reg_date_st . "' and '" . $reg_date_en . "') ";
                }

                //dump($reg_where);
                $ck = DB::connection(config('A_sub'))->table('excel')->whereRaw($reg_where)->groupBy('account_number')->select('account_number')->get();
                $ck = CommonService::objectToArray($ck);
                $rowData[$key]['reg'] = '0';
                if ($ck) {
                    $rowData[$key]['reg'] = '1';
                    $do_number++;
                    array_push($h_number, $rowData[$key]);
                } else {
                    array_push($n_number, $rowData[$key]);
                }
            }
        } else if ($nameKey == "2") { // 購買商品搜尋
            foreach ($rowData as $key => $value) {
                $his_wh = '';

                if ($searchKey2 != '') {
                    $his_wh = "product like '%" . $searchKey2 . "%'  and";
                }
                if ($buy_date_st2 != '') {
                    $his_wh .= " create_time > '" . strtotime($buy_date_st2) . "' and create_time <'" . (strtotime($buy_date_en2) + 86400) . "' and ";
                }

                //dump($his_wh);
                $ck = self::main_db()->table(self::$orderTableName)->select('user_id')
                    ->whereRaw($his_wh . "  user_id = '" . $value['id'] . "'")
                    ->groupBy('user_id')
                    ->get();
                $ck = CommonService::objectToArray($ck);
                $rowData[$key]['buy'] = '0';

                if ($ck) {
                    $rowData[$key]['buy'] = '1';
                    $do_number++;
                    array_push($h_number, $rowData[$key]);
                } else {
                    array_push($n_number, $rowData[$key]);
                }
            }
        }
        $search_result['do_number'] = $do_number;
        $search_result['h_number'] = $h_number;
        $search_result['n_number'] = $n_number;

        return $search_result;
    }
    /*依搜尋條件回傳屬於的user_id sql篩選語法*/
    static public function user_id_sql($cond = [])
    {
        $user_id_sql = "";
        if (empty($cond)) {
            return $user_id_sql;
        }

        $users = self::main_db()->table(self::$tableName . ' as a');

        if (isset($cond['searchKey'])) { /*關鍵字篩選(姓名、會員編號)*/
            if ($cond['searchKey']) {
                $users = $users->where(function ($query) use ($cond) {
                    $query->where('name', 'like', "%" . $cond['searchKey'] . "%")
                        ->orWhere('number', 'like', "%" . $cond['searchKey'] . "%");
                });
            }
        }
        $users = $users->get();
        $users = CommonService::objectToArray($users);
        $user_ids = [];
        foreach ($users as $value) {
            array_push($user_ids, $value['id']);
        }
        if ($user_ids) {
            $user_id_sql = 'user_id in (' . implode(",", $user_ids) . ')';
        }

        return $user_id_sql;
    }

    /*取得新會員編號*/
    static public function getMemberNumber()
    {
        $count = self::main_db()->table(self::$tableName)->where('number', 'like', config('extra.shop.subDeparment') . 'US' . date('Ymd') . '%')->orderByRaw('id desc')->first();
        $count = CommonService::objectToArray($count);
        $count = $count ? intval(substr($count['number'], -3)) + 1 : 1;
        if ($count < 10) {
            $count = '00' . $count;
        } else if ($count < 100) {
            $count = '0' . $count;
        }
        return $count;
    }
    /*調整資料格式以符合資料庫*/
    static public function arrange_data_to_db_format($data)
    {
        $returnData = ['code' => 0, 'msg' => "", 'data' => $data];

        /*設定檢查*/
        /*必填欄位*/
        $rule = [];
        $msg = [];
        if (isset($data['email'])) {
            if ($data['email']) {
                $rule['email'] = 'required|email';
                $msg['email.email'] = Lang::get('email格式錯誤');
                $msg['email.required'] = Lang::get('email不得為空');
            }
        }
        if (isset($data['name'])) {
            $rule['name'] = 'required';
            $msg['name.required'] = Lang::get('名稱不得為空');
        }
        if (isset($data['F_S_NH_Address'])) {
            $rule['F_S_NH_Address'] = 'required';
            $msg['F_S_NH_Address.required'] = Lang::get('地址不得為空');
        }
        // if( isset($data['F_I_CNo']) ){
        // 	$rule['F_I_CNo'] = 'required';
        // 	$msg['F_I_CNo.required'] = Lang::get('請選擇縣市'];
        // }
        // if( isset($data['F_I_TNo']) ){
        // 	$rule['F_I_TNo'] = 'required';
        // 	$msg['F_I_TNo.required'] = Lang::get('請選擇鄉鎮區'];
        // }
        // if( isset($data['F_S_NH_Zip']) ){
        // 	$rule['F_S_NH_Zip'] = 'required';
        // 	$msg['F_S_NH_Zip.required'] = Lang::get('郵遞區號不得為空'];
        // }
        /*進行驗證*/
        $validate = Validator::make($data, $rule, $msg);
        // $validate->rule('regex', '/^.[A-Za-z0-9]+$/');
        if ($validate->fails()) {
            $returnData['msg'] = $validate->errors()->first();
            return $returnData;
        }

        if (isset($data['phone'])) {
            // if(!preg_match("/^09[0-9]{8}$/", $data['phone'])){
            //   $returnData['msg'] = Lang::get('請輸入09開頭的10位手機號碼');
            //   return $returnData;
            // }
            if (!preg_match("/^\d+$/", $data['phone'])) {
                $returnData['msg'] = Lang::get('手機號碼限輸入數字');
                return $returnData;
            }
        }

        /*檢查密碼*/
        if (isset($data['password']) || isset($data['pwd'])) {
            $password = isset($data['password']) ? $data['password'] : "";
            $password = isset($data['pwd']) ? $data['pwd'] : $password;
            if (!preg_match('/([0-9]+)/', $password) || !preg_match('/([a-zA-Z]+)/', $password)) {
                $returnData['msg'] = Lang::get('密碼需包含英文及數字');
                return $returnData;
            }
            if (isset($data['passwordB'])) {
                if ($password != $data['passwordB']) {
                    $returnData['msg'] = Lang::get('密碼不一致');
                    return $returnData;
                }
            }
            $data['pwd'] = $password;
        }

        /*資料修改格式*/
        /*密碼*/
        if (isset($data['pwd'])) {
            $data['pwd'] = md5($data['pwd']);
        }

        /*生日*/
        if (isset($data['birthday'])) {
            if ($data['birthday']) {
                $data['birthday'] = strtotime($data['birthday']);
                if (!$data['birthday']) {
                    $returnData['msg'] = Lang::get('生日格式請輸入YYYY/MM/DD');
                    return $returnData;
                }
            }
        }

        /*地址*/
        $home = "";
        if (isset($data['F_I_CNo']) && isset($data['F_I_TNo']) && isset($data['F_S_NH_Zip'])) {
            if ($data['F_I_CNo'] != '' && $data['F_I_TNo'] != '' && $data['F_S_NH_Zip'] != '') {
                $home = $data['F_I_CNo'] . '|||' . $data['F_I_TNo'] . '|||' . $data['F_S_NH_Zip'] . '|||';
            }
        }
        if (isset($data['F_S_NH_Address'])) {
            $home .= $data['F_S_NH_Address'];
        }
        if ($home) {
            $data['home'] = $home;
        }

        /*刪除非資料庫欄位的輸入值*/
        unset($data['F_I_CNo']);
        unset($data['F_I_TNo']);
        unset($data['F_S_NH_Zip']);
        unset($data['F_S_NH_Address']);

        unset($data['password']);
        unset($data['passwordB']);
        unset($data['term']);

        unset($data['recaptcha']);
        unset($data['g-recaptcha-response']);

        $config = \HTMLPurifier_Config::createDefault();
        $purifier = new \HTMLPurifier($config);
        foreach ($data as $key => $value) {
            $data[$key] = $purifier->purify($value);
        }

        $returnData['code'] = 1;
        $returnData['data'] = $data;
        return $returnData;
    }

    /*取得會員來源資料*/
    static public function get_registration_from(array $params = [], bool $id_as_key = false)
    {
        $db_data = [
            ['id' => 1, 'name' => '會員'],
            ['id' => 2, 'name' => '廣告'],
        ];

        if (isset($params['id'])) {
            $db_data = array_filter($db_data, function ($item) use ($params) {
                return $params['id'] == $item['id'];
            });
        }

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }

    /*取得VIP會員等級資料*/
    static public function get_vip_types(array $params = [], bool $id_as_key = false)
    {
        $db_data = DB::connection('main_db')->table('vip_type')->whereRaw("id != 0");

        if (isset($params['id'])) {
            $db_data->where('id', $params['id']);
        }

        $db_data = $db_data->orderby('rule', 'asc')->orderby('id', 'asc')->get();
        $db_data = CommonService::objectToArray($db_data);

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }

    /**
     * 更新VIP會員等級
     */
    public function update_vip_type(int $vip_type)
    {
        $vip_relation = Db::connection('main_db')->table('vip_type_relation')
            ->where('user_id', $this->user_id)
            ->orderByRaw('id desc')->get();
        $vip_relation = CommonService::objectToArray($vip_relation);
        if ($vip_relation) { // 如果有紀錄
            if ($vip_relation[0]['vip_type_id'] == $vip_type) { // 最新一筆紀錄與此次修改相同
                return; // 略過新增紀錄
            }
        }

        // 修改account的vip_type
        $this->update_user_data([
            'vip_type' => $vip_type
        ]);

        // 新增紀錄
        $recordData = [
            'user_id' => $this->user_id,
            'vip_type_id' => $vip_type,
            'datetime' => date('Y-m-d H:i:s'),
        ];
        Db::connection('main_db')->table('vip_type_relation')->insert($recordData);
    }

    /*取得合夥等級資料*/
    static public function get_partner_levels(array $params = [], bool $id_as_key = false)
    {
        $db_data = DB::connection('main_db')->table('partner_level');

        if (isset($params['id'])) {
            $db_data->where('id', $params['id']);
        }

        $db_data = $db_data->orderBy('contribution', 'asc')->orderBy('id', 'asc')->get();
        $db_data = CommonService::objectToArray($db_data);

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }

    /*儲存合夥等級資料(根據傳入資料的id==0與否判斷為新增或編輯)*/
    static public function save_partner_level($detail_data)
    {
        if (!isset($detail_data['id'])) {
            throw new \Exception(Lang::get('資料不完整'));
        }

        $detail_data['name'] = $detail_data['name'] ?? '';
        if ($detail_data['name'] == '') {
            throw new \Exception('請輸入名稱');
        }

        $detail_data['ratio'] = $detail_data['ratio'] ?? '';
        if ($detail_data['ratio'] == '') {
            throw new \Exception('請輸入功德圓滿點數倍率');
        } else if ($detail_data['ratio'] < 0) {
            throw new \Exception('功德圓滿點數倍率應大於0');
        }

        $detail_data['contribution'] = $detail_data['contribution'] ?? '';
        if ($detail_data['contribution'] == '') {
            throw new \Exception('請輸入累積投資金額');
        } else if ($detail_data['contribution'] < 0) {
            throw new \Exception('累積投資金額應大於0');
        }

        $detail_data['partner_bonus_ratio'] = $detail_data['partner_bonus_ratio'] ?? '';
        if ($detail_data['partner_bonus_ratio'] == '') {
            throw new \Exception('請輸入合夥平級獎勵可回饋比率');
        } else if ($detail_data['partner_bonus_ratio'] < 0 || $detail_data['partner_bonus_ratio'] > 100) {
            throw new \Exception('合夥平級獎勵可回饋比率應介於0~100');
        }

        $detail_data['orderform_ad_weight'] = $detail_data['orderform_ad_weight'] ?? '';
        if ($detail_data['orderform_ad_weight'] == '') {
            throw new \Exception('請輸入廣告訂單加權');
        } else if ($detail_data['orderform_ad_weight'] < 0) {
            throw new \Exception('廣告訂單加權應大於0');
        }

        // dd($detail_data);
        $DBTextConnecter = DBTextConnecter::withTableName('partner_level', 'main_db');
        if ($detail_data['id'] == 0) { /*新增*/
            unset($detail_data['id']);
            $DBTextConnecter->setDataArray($detail_data);
            $id = $DBTextConnecter->createTextRow();
        } else { /*編輯*/
            $DBTextConnecter->setDataArray($detail_data);
            $DBTextConnecter->upTextRow();
            $id = $detail_data['id'];
        }
        return $id;
    }
    /* 刪除合夥等級資料 */
    static public function delete_partner_level($item_id)
    {
        if (!$item_id) {
            throw new \Exception(Lang::get('資料不完整'));
        }

        $db_data = DB::connection('main_db')->table('partner_level');
        $db_data = $db_data->where('id', $item_id);
        return $db_data->delete();
    }
    /* 更新會員的合夥等級 */
    public function update_partner_level(int $new_level_id, $admin_force = false)
    {
        $user_data = $this->get_user_data('ori');
        $pre_level_id = $user_data['partner_level_id'] ?? 0;

        $check_result = $this->check_partner_level($pre_level_id, $new_level_id);
        if ($check_result['msg'] && $admin_force) {/*調整等級有誤 但 是後台硬調整*/
            $check_result['msg'] = ''; /*硬調整成無錯誤訊息*/
        }
        if ($check_result['msg'] == '') { /*無錯誤訊息*/
            // 更新會員狀態
            $this->update_user_data(
                [
                    'partner_level_id' => $new_level_id,
                    'partner_accumulation' => $this->arr_partner_levels[$new_level_id]['contribution'] ?? 0,
                ],
                [['a.id', $this->user_id]],
                false
            );

            // 依檢查結果添加等級關係紀錄
            $this->create_partner_level_relation($check_result['pre_rank'], $check_result['new_rank']);
        }
        return $check_result;
    }

    /**
     * 依傳入等級與目前會員的合夥等級做異動檢查
     */
    public function check_partner_level(int $pre_level_id, int $new_level_id)
    {
        $type_key_rank = array_keys($this->arr_partner_levels);
        $pre_rank = array_search($pre_level_id, $type_key_rank);
        $pre_rank = $pre_rank !== false ? $pre_rank : -1;
        $new_rank = array_search($new_level_id, $type_key_rank);
        $new_rank = $new_rank !== false ? $new_rank : -1;
        if ($pre_rank > $new_rank) {
            return [
                'msg' => '不允許降低合夥等級',
                'pre_level_id' => $pre_level_id,
                'new_level_id' => $pre_level_id,
                'pre_rank' => $pre_rank,
                'new_rank' => $pre_rank,
            ];
        }
        return [
            'msg' => '',
            'pre_level_id' => $pre_level_id,
            'new_level_id' => $new_level_id,
            'pre_rank' => $pre_rank,
            'new_rank' => $new_rank,
        ];
    }

    /**
     * 依傳入等級排名(前&後)建立會員的合夥等級關係紀錄
     */
    public function create_partner_level_relation(int $pre_rank, int $new_rank)
    {
        $datetime = date('Y-m-d H:i:s');
        $type_key_rank = array_keys($this->arr_partner_levels);

        /*撈取最後紀錄並作為檢查依據(避免重複)*/
        $type_relation = Db::connection('main_db')->table('partner_level_relation')
            ->where('user_id', $this->user_id)
            ->orderByRaw('id desc')->get();
        $type_relation = CommonService::objectToArray($type_relation);
        $relation_level_id = $type_relation[0]['level_id'] ?? 0;
        $relation_rank = array_search($relation_level_id, $type_key_rank);
        $relation_rank = $relation_rank !== false ? $relation_rank : -1;

        // 新增紀錄/*每個等級都要拆分開來建立記錄(推3反本用)*/
        $rank_changes = [];
        foreach ($type_key_rank as $rank => $level_id) {
            if ($rank <= $pre_rank) {
                continue;
            }       /*等級小等於過去階級*/
            if ($rank > $new_rank) {
                break;
            }           /*等級大於新的階級*/
            if ($rank <= $relation_rank) {
                continue;
            }  /*等級小等於過去已建立過關係的階級*/
            array_push($rank_changes, [
                'user_id' => $this->user_id,
                'level_id' => $level_id,
                'datetime' => $datetime,
            ]);
        }
        // dump($rank_changes);exit;
        if (count($rank_changes) > 0) {
            Db::connection('main_db')->table('partner_level_relation')->insert($rank_changes);
        }
    }

    /*取得中心等級資料*/
    static public function get_center_levels(array $params = [], bool $id_as_key = false)
    {
        $db_data = DB::connection('main_db')->table('center_level');

        if (isset($params['id'])) {
            $db_data->where('id', $params['id']);
        }

        $db_data = $db_data->orderBy('orders', 'asc')->orderBy('id', 'asc')->get();
        $db_data = CommonService::objectToArray($db_data);

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }
    /*儲存中心等級資料(根據傳入資料的id==0與否判斷為新增或編輯)*/
    static public function save_center_level($detail_data)
    {
        if (!isset($detail_data['id'])) {
            throw new \Exception(Lang::get('資料不完整'));
        }

        $detail_data['name'] = $detail_data['name'] ?? '';
        if ($detail_data['name'] == '') {
            throw new \Exception('請輸入名稱');
        }

        $detail_data['orders'] = $detail_data['orders'] ?? '';
        if ($detail_data['orders'] == '') {
            throw new \Exception('請輸入排序');
        }

        $detail_data['cv_ratio'] = $detail_data['cv_ratio'] ?? '';
        if ($detail_data['cv_ratio'] == '') {
            throw new \Exception('請輸入CV金額分潤比率');
        } else if (!is_numeric($detail_data['cv_ratio'])) {
            throw new \Exception('CV金額分潤比率必須為數字');
        } else if ($detail_data['cv_ratio'] < 0) {
            throw new \Exception('CV金額分潤比率不可小於0');
        } else if ($detail_data['cv_ratio'] > 100) {
            throw new \Exception('CV金額分潤比率不可大於100');
        }

        // dd($detail_data);
        $DBTextConnecter = DBTextConnecter::withTableName('center_level', 'main_db');
        if ($detail_data['id'] == 0) { /*新增*/
            unset($detail_data['id']);
            $DBTextConnecter->setDataArray($detail_data);
            $id = $DBTextConnecter->createTextRow();
        } else { /*編輯*/
            $DBTextConnecter->setDataArray($detail_data);
            $DBTextConnecter->upTextRow();
            $id = $detail_data['id'];
        }
        return $id;
    }
    /* 刪除中心等級資料 */
    static public function delete_center_level($item_id)
    {
        if (!$item_id) {
            throw new \Exception(Lang::get('資料不完整'));
        }

        $db_data = DB::connection('main_db')->table('center_level');
        $db_data = $db_data->where('id', $item_id);
        return $db_data->delete();
    }
}
