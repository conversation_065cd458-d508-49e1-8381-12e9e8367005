<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateOrderformTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->table('orderform', function (Blueprint $table) {
            // 1. 改名 user_id_operation -> user_id_marketing_dept
            $table->renameColumn('user_id_operation', 'user_id_marketing_dept'); //再更改欄位名..

            // 5. 新增 user_id_center_founder
            $table->integer('user_id_center_founder', false, true)->length(11)->default(0)->comment('中心發起人(對應account的id)')->after('user_id_operation');

            // 4. 新增 user_id_center_director
            $table->integer('user_id_center_director', false, true)->length(11)->default(0)->comment('中心總監(對應account的id)')->after('user_id_operation');

            // 3. 新增 user_id_executive_director
            $table->integer('user_id_executive_director', false, true)->length(11)->default(0)->comment('大總監(對應account的id)')->after('user_id_operation');

            // 2. 新增 user_id_sales_dept
            $table->integer('user_id_sales_dept', false, true)->length(11)->default(0)->comment('業務部門(對應account的id)')->after('user_id_operation');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->table('orderform', function (Blueprint $table) {
            // 還原欄位名稱
            $table->renameColumn('user_id_marketing_dept', 'user_id_operation');

            // 刪除新增欄位
            $table->dropColumn('user_id_sales_dept');
            $table->dropColumn('user_id_executive_director');
            $table->dropColumn('user_id_center_director');
            $table->dropColumn('user_id_center_founder');
        });
    }
}
